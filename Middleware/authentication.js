const jwkToPem = require('jwk-to-pem');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');

const { validationResult } = require('express-validator');

const {
    users: UserSchema,
    user_sessions: UserSessionSchema,
    user_roles: UserRoleSchema,
    tenant_customers: TenantCustomerSchema,
    tenants: TenantSchema,
} = require('../Database/Schemas');

const UserAuthModal = new (require("../Models/auth"))();
const sendFailureEmail = require('./SendFailureEmail').default

const {
    VALUES,
    deviceTypeDetected,
    PRIMITIVE_ROLES,
    TENANT_SERVICES,
} = require('../Configs/constants');

const { stringifyObjectId } = require('../Utils/helpers');

// In-memory cache for DataSync authentication
class DataSyncAuthCache {
    constructor() {
        this.cache = new Map();
        this.maxSize = 1000;
        this.cleanupInterval = 60000; // 1 minute
        this.startCleanupTimer();
    }

    createCacheKey(authData) {
        try {
            console.log('🔧 Creating cache key from auth data...');
            
            // Create a simple key format: authorization-devicetoken-refreshtoken-userid
            const authKey = authData.authorization || 'no-auth';
            const deviceKey = authData.devicetoken || 'no-device';
            const refreshKey = authData.refreshtoken || 'no-refresh';
            const userKey = authData.userid || 'no-user';
            
            const cacheKey = `${authKey}-${deviceKey}-${refreshKey}-${userKey}`;
            
            // Create a hash for security and consistent length
            const hash = crypto.createHash('sha256').update(cacheKey).digest('hex');
            const finalKey = `datasync_auth_${hash}`;
            
            console.log('📋 Auth components:', { authKey, deviceKey, refreshKey, userKey });
            console.log('🔑 Generated cache key:', finalKey);
            return finalKey;
        } catch (error) {
            console.error('❌ Error creating cache key:', error);
            return `datasync_auth_fallback_${Date.now()}`;
        }
    }

    calculateCacheExpiry(accessToken, refreshToken, defaultExpiry = 600) {
        try {
            let earliestExpiry = defaultExpiry;

            if (accessToken) {
                try {
                    const decodedAccess = jwt.decode(accessToken, { complete: true });
                    if (decodedAccess && decodedAccess.payload.exp) {
                        const accessExpiry = Math.floor((decodedAccess.payload.exp * 1000 - Date.now()) / 1000);
                        if (accessExpiry > 0 && accessExpiry < earliestExpiry) {
                            earliestExpiry = accessExpiry;
                        }
                    }
                } catch (error) {
                    console.warn('Failed to decode access token for cache expiry:', error.message);
                }
            }

            if (refreshToken) {
                try {
                    const decodedRefresh = jwt.decode(refreshToken, { complete: true });
                    if (decodedRefresh && decodedRefresh.payload.exp) {
                        const refreshExpiry = Math.floor((decodedRefresh.payload.exp * 1000 - Date.now()) / 1000);
                        if (refreshExpiry > 0 && refreshExpiry < earliestExpiry) {
                            earliestExpiry = refreshExpiry;
                        }
                    }
                } catch (error) {
                    console.warn('Failed to decode refresh token for cache expiry:', error.message);
                }
            }

            return Math.max(earliestExpiry, 30);
        } catch (error) {
            console.error('Error calculating cache expiry:', error);
            return defaultExpiry;
        }
    }

    setCache(key, value, expirySeconds) {
        try {
            console.log(`💾 Setting cache for key: ${key}`);
            console.log(`⏰ Expiry: ${expirySeconds} seconds`);
            
            if (this.cache.size >= this.maxSize) {
                console.log(`⚠️ Cache full (${this.cache.size}/${this.maxSize}), evicting oldest entries`);
                this.evictOldestEntries();
            }

            const expiryTime = Date.now() + (expirySeconds * 1000);
            const cacheEntry = {
                value: value,
                expiry: expiryTime,
                created: Date.now(),
                lastAccessed: Date.now()
            };

            this.cache.set(key, cacheEntry);
            
            console.log(`✅ Cache SET: ${key}, expires in ${expirySeconds}s`);
            console.log(`📊 Current cache size: ${this.cache.size}/${this.maxSize}`);
        } catch (error) {
            console.error('❌ Error setting cache:', error);
        }
    }

    getCache(key) {
        try {
            console.log(`🔍 Looking up cache for key: ${key}`);
            const entry = this.cache.get(key);
            
            if (!entry) {
                console.log(`❌ Cache MISS: ${key}`);
                return null;
            }

            if (Date.now() > entry.expiry) {
                this.cache.delete(key);
                console.log(`⏰ Cache EXPIRED: ${key}`);
                return null;
            }

            entry.lastAccessed = Date.now();
            this.cache.set(key, entry);
            
            console.log(`✅ Cache HIT: ${key}`);
            return entry.value;
        } catch (error) {
            console.error('❌ Error getting cache:', error);
            return null;
        }
    }

    // Method to check if headers match for debugging cache misses
    checkHeadersMatch(key, currentHeaders) {
        try {
            console.log(`🔍 Checking headers match for cache key: ${key}`);
            
            // Get the cached entry to compare headers
            const entry = this.cache.get(key);
            if (!entry) {
                console.log('❌ No cached entry found to compare');
                return false;
            }

            // Extract the original headers from the cached data
            const cachedHeaders = entry.value.originalHeaders || {};
            
            console.log('📋 Current Headers:', JSON.stringify(currentHeaders, null, 2));
            console.log('📋 Cached Headers:', JSON.stringify(cachedHeaders, null, 2));
            
            // Compare key header values
            const keyFields = ['authorization', 'devicetoken', 'refreshtoken', 'userroleid'];
            let matchCount = 0;
            let totalFields = 0;
            
            for (const field of keyFields) {
                const currentValue = currentHeaders[field];
                const cachedValue = cachedHeaders[field];
                
                if (currentValue && cachedValue) {
                    totalFields++;
                    if (currentValue === cachedValue) {
                        matchCount++;
                        console.log(`✅ ${field}: MATCH`);
                    } else {
                        console.log(`❌ ${field}: MISMATCH`);
                        console.log(`   Current: ${currentValue}`);
                        console.log(`   Cached:  ${cachedValue}`);
                    }
                } else {
                    console.log(`⚠️ ${field}: Missing in one or both`);
                    console.log(`   Current: ${currentValue || 'undefined'}`);
                    console.log(`   Cached:  ${cachedValue || 'undefined'}`);
                }
            }
            
            const matchPercentage = totalFields > 0 ? (matchCount / totalFields) * 100 : 0;
            console.log(`📊 Header Match: ${matchCount}/${totalFields} (${matchPercentage.toFixed(1)}%)`);
            
            return matchCount === totalFields && totalFields > 0;
        } catch (error) {
            console.error('❌ Error checking headers match:', error);
            return false;
        }
    }

    clearExpiredCache() {
        try {
            const now = Date.now();
            let expiredCount = 0;

            for (const [key, entry] of this.cache.entries()) {
                if (now > entry.expiry) {
                    this.cache.delete(key);
                    expiredCount++;
                }
            }

            if (expiredCount > 0) {
                console.log(`Cleared ${expiredCount} expired cache entries`);
            }
        } catch (error) {
            console.error('Error clearing expired cache:', error);
        }
    }

    evictOldestEntries() {
        try {
            const entries = Array.from(this.cache.entries());
            const sortedEntries = entries.sort((a, b) => a[1].lastAccessed - b[1].lastAccessed);
            const removeCount = Math.ceil(this.maxSize * 0.2);
            
            for (let i = 0; i < removeCount && i < sortedEntries.length; i++) {
                this.cache.delete(sortedEntries[i][0]);
            }
            
            console.log(`Evicted ${removeCount} oldest cache entries`);
        } catch (error) {
            console.error('Error evicting cache entries:', error);
        }
    }

    startCleanupTimer() {
        setInterval(() => {
            this.clearExpiredCache();
        }, this.cleanupInterval);
    }

    // Method to update cache when access token is refreshed
    updateCacheWithNewToken(oldToken, newToken, requestHeaders) {
        try {
            console.log('🔄 Updating cache with new access token...');
            
            // Find and remove old cache entries with the old token
            let removedCount = 0;
            for (const [key, entry] of this.cache.entries()) {
                if (entry.value.originalHeaders && 
                    entry.value.originalHeaders.authorization === oldToken) {
                    this.cache.delete(key);
                    removedCount++;
                    console.log(`🗑️ Removed old cache entry: ${key}`);
                }
            }
            
            if (removedCount > 0) {
                console.log(`✅ Removed ${removedCount} old cache entries with expired token`);
            }
            
            return true;
        } catch (error) {
            console.error('❌ Error updating cache with new token:', error);
            return false;
        }
    }
}

// Create singleton cache instance
const datasyncCache = new DataSyncAuthCache();

exports.authentication = async (request, response, next) => {
    const errors = validationResult(request).formatWith(({ msg }) => msg)

    if (!errors.isEmpty()) {
        sendFailureEmail(request, errors.array())
            .catch(error => console.error('SendFailureEmail error:', error))

        return response.handler.badRequest(
            undefined,
            errors.array()
        );
    }

    // USED FOR WHEN AUTHENTICATION IS OPTIONAL
    if (!request.headers.authorization
        || request.originalUrl.includes('/auth/app-sign-in')
        || request.originalUrl.includes('/auth/sign-in')
        || request.originalUrl.includes('/auth/verify-auth-otp')
    ) {
        return next();
    }

    // Check cache for DataSync routes before proceeding with authentication
    if (request.originalUrl.includes('/dataSync') || request.headers.datasync === 'true') {
        console.log('🔍 DataSync Auth Cache Check for:', request.originalUrl);
        console.log('📋 Request Headers:', JSON.stringify(request.headers, null, 2));
        
        const cacheKey = datasyncCache.createCacheKey({
            authorization: request.headers.authorization,
            devicetoken: request.headers.devicetoken,
            refreshtoken: request.headers.refreshtoken ,
            userid: request.headers.userroleid
        });
        console.log('🔑 Generated Cache Key:', cacheKey);
        
        const cachedAuth = datasyncCache.getCache(cacheKey);
        console.log('💾 Cached Auth Result:', cachedAuth ? 'FOUND' : 'NOT FOUND');
        
        if (cachedAuth) {
            console.log('✅ DataSync Auth Cache HIT - Applying cached data');
            // Apply cached authentication data to request
            request.headers.sessionDetails = cachedAuth.sessionDetails;
            request.headers.portalType = cachedAuth.portalType;
            request.headers.tenantDetail = cachedAuth.tenantDetail;
            request.headers.userDetails = cachedAuth.userDetails;
            request.headers.deviceTypeDetected = cachedAuth.deviceTypeDetected;
            
            console.log('🚀 DataSync Auth Cache HIT for:', request.originalUrl);
            return next();
        }
        
        console.log('❌ DataSync Auth Cache MISS - Proceeding with normal authentication');
        
        // Check if there are any cached entries and compare headers to debug the miss
        if (datasyncCache.cache.size > 0) {
            console.log('🔍 Debugging cache miss - checking header differences...');
            
            // Get all cache keys and check headers
            const cacheKeys = Array.from(datasyncCache.cache.keys());
            console.log(`📊 Found ${cacheKeys.length} cached entries`);
            
            // Check the first few cache entries for header comparison
            const keysToCheck = cacheKeys.slice(0, 3); // Check first 3 entries
            
            for (const key of keysToCheck) {
                console.log(`\n🔍 Checking cache entry: ${key}`);
                datasyncCache.checkHeadersMatch(key, request.headers);
            }
        } else {
            console.log('📊 No cached entries found - this is the first request');
        }
    }

    // TO discard saving the refreshed-access-token in the browser (browser by default saves the response headers). if needed add this header to non protected routs too.
    // response.setHeader("Cache-Control", "max-age=0");
    response.setHeader("Cache-Control", "no-cache");

    const userRoleDetails = await UserRoleSchema
        .findById(request.headers.userroleid)
        .populate({ path: "role_id" })
        .populate({ path: "user_id" })
        .populate({
            path: "tenant_id",
            select: 'services',
            populate: {
                path: "country",
                model: "countries",
            },
        })
        .lean();

    const logErrorData = (data = {}) => {
        const logData = { ...data }

        const userRoleInfo =
            data.userRoleDetails ||
            request.headers.sessionDetails ||
            userRoleDetails

        if (userRoleInfo) {
            logData["customer_name"] = userRoleInfo.user_id?.customer_name || userRoleInfo.user_id?.customer_legal_name
            logData["customer_email"] = userRoleInfo.user_id?.customer_email
            logData["role_id"] = {
                "_id": userRoleInfo.role_id?._id,
                "name": userRoleInfo.role_id?.name,
            }
            logData["last_mobile_login_time"] = userRoleInfo.last_mobile_login_time
            logData["last_tablet_login_time"] = userRoleInfo.last_tablet_login_time
        }
        return logData
    }

    if (request.headers.userroleid) {
        let message;

        if (!userRoleDetails) {
            message = "validation_not_found_role";
        }
        else if (userRoleDetails && !userRoleDetails.is_active) {
            message = "profile_is_inactive";
        }
        else if (userRoleDetails && userRoleDetails.is_deleted) {
            message = "profile_is_deleted";
        }
        else if (userRoleDetails.role_id.name === PRIMITIVE_ROLES.CUSTOMER) {
            const {
                customer_app_access,
                tenant_id,
                device_access = [],
            } = userRoleDetails

            // Validates if customer still holds app access or not
            if (customer_app_access) {
                const tenantServices = await TenantSchema.findOne(
                    { _id: tenant_id },
                    { services: 1 },
                    { lean: true }
                );

                if (!tenantServices.services?.find(s => s.key === TENANT_SERVICES.CUSTOMER_APP)?.permission?.view) {
                    message = 'tenant_app_access_not_active'
                }
            }
            else {
                message = "app_access_not_active";
            }

            /**
             * @variation Below routes excluded as from mobile unwanted userRoleId is coming.
             * Mobile team has to fix it then we can remove below conditions
             */
            if (
                !request.originalUrl.includes("/auth/app-user-roles") &&
                !request.originalUrl.includes("/auth/user-role-accessed")
            ) {
                // Validate if customer still holds valid recognized device or not
                const deviceErrorMessage = "device_no_longer_recognized"

                if (device_access.length) {
                    const deviceId = request.headers.devicetoken
                    const deviceType = request.headers.deviceaccesstype
                    const deviceOs = request.headers.devicetype

                    const hasValidDevice = device_access.find(device =>
                        device.device_id === deviceId &&
                        device.type === deviceType &&
                        device.os === deviceOs
                    )

                    if (!hasValidDevice) {
                        message = deviceErrorMessage
                    }
                }
                else {
                    message = deviceErrorMessage
                }
            }
        }
        if (message) {
            const data = logErrorData()
            return response.handler.unauthorized(message, data);
        }
    }
    const portal_type = userRoleDetails?.role_id.portal_type

    request.headers.sessionDetails = userRoleDetails;
    request.headers.portalType = portal_type;
    request.headers.tenantDetail = userRoleDetails?.tenant_id;

    let checkCognitoToken = false;
    request.headers.deviceTypeDetected = deviceTypeDetected.APPLICATION;

    try {
        const {
            user,
            tenant_customer,
            deviceaccesstype,
            login_time,
        } = jwt.verify(request.headers.authorization, process.env.JWT_TOKEN_SECRET);

        if (
            [
                VALUES.portals.SALES_APP,
                VALUES.portals.TENANT_PORTAL,
                VALUES.portals.SUPERVISOR_APP,
            ].includes(portal_type)
        ) {
            if (
                request.headers.userroleid &&
                (
                    !user ||
                    (stringifyObjectId(userRoleDetails.user_id._id) !== stringifyObjectId(user._id))
                )
            ) {
                const errorMessage = "invalid_user_id"
                const data = logErrorData({ user })
                const customError = new Error(errorMessage)

                return response.handler.unauthorized(errorMessage, data, customError);
            }
            request.headers.userDetails = await UserSchema.findById(user._id).lean()
        }
        else {
            if (
                request.headers.userroleid &&
                (
                    !tenant_customer ||
                    (stringifyObjectId(userRoleDetails.user_id._id) !== stringifyObjectId(tenant_customer._id))
                )
            ) {
                const errorMessage = "invalid_user_id"
                const data = logErrorData({ tenant_customer })
                const customError = new Error(errorMessage)

                return response.handler.unauthorized(errorMessage, data, customError);
            }
            request.headers.userDetails = await TenantCustomerSchema.findById(tenant_customer).lean()
        }

        // DO NOT CHECK FOR ('/auth/user-role-accessed')
        if (
            (deviceaccesstype === VALUES.deviceAccessType.MOBILE) // TODO: need to remove this check if in future, we need to maintain single device sign in for TABLET too
            && (portal_type === VALUES.portals.SALES_APP) // TODO: need to remove this check if in future, we need to maintain single device sign in for all roles of tenant
            && (!request.originalUrl.includes("/auth/user-role-accessed"))
            && (!request.originalUrl.includes('/auth/app-user-roles'))
        ) {
            const tokenLoginTime = moment(login_time);
            if (!tokenLoginTime.isValid()) {
                const errorMessage = "invalid_token_found"

                const data = logErrorData({
                    tokenLoginTime,
                    login_time,
                })
                const customError = new Error(errorMessage)

                return response.handler.unauthorized(errorMessage, data, customError);
            }

            const dbLoginTime = deviceaccesstype === VALUES.deviceAccessType.MOBILE
                ? userRoleDetails?.last_mobile_login_time
                : userRoleDetails?.last_tablet_login_time;

            if ((moment(dbLoginTime).toISOString() !== login_time) || (!dbLoginTime)) { // check for latest session time and token's login time
                const errorMessage = "invalid_session"

                const data = logErrorData({
                    dbLoginTime,
                    login_time,
                })
                const customError = new Error(errorMessage)

                return response.handler.unauthorized(errorMessage, data, customError);
            }
        }
    }
    catch (error) {
        switch (error.name) {
            case "TokenExpiredError":
                {
                    try {
                        const { login_time, deviceaccesstype } = jwt.decode(request.headers.authorization);

                        let loginTime;
                        let lastLoginDbField;

                        if (deviceaccesstype === VALUES.deviceAccessType.MOBILE) {
                            lastLoginDbField = 'last_mobile_login_time';
                        }
                        else {
                            lastLoginDbField = 'last_tablet_login_time';
                        }
                        loginTime = userRoleDetails[lastLoginDbField];

                        if (
                            (deviceaccesstype === VALUES.deviceAccessType.MOBILE) // TODO: need to remove this check if in future, we need to maintain single device sign in for TABLET too
                            && (portal_type === VALUES.portals.SALES_APP) // TODO: need to remove this check if in future, we need to maintain single device sign in for all roles of tenant
                        ) {
                            if (!login_time || !loginTime) {
                                const errorMessage = "token_login_time_not_found"

                                const data = logErrorData({
                                    login_time_from_token: login_time,
                                    last_device_login_time_from_db: loginTime,
                                })
                                const customError = new Error(errorMessage)

                                return response.handler.unauthorized(errorMessage, data, customError);
                            }

                            if (!deviceaccesstype || ((deviceaccesstype !== VALUES.deviceAccessType.MOBILE) && (deviceaccesstype !== VALUES.deviceAccessType.TABLET))) {
                                const errorMessage = "deviceaccesstype_not_found"
                                const data = logErrorData({ deviceaccesstype })
                                const customError = new Error(errorMessage)

                                return response.handler.unauthorized(errorMessage, data, customError);
                            }

                            if (loginTime.toISOString() !== login_time) {
                                const errorMessage = "invalid_session"

                                const data = logErrorData({
                                    login_time_from_token: login_time,
                                    last_device_login_time_from_db: loginTime.toISOString(),
                                })
                                const customError = new Error(errorMessage)

                                return response.handler.unauthorized(errorMessage, data, customError);
                            }
                        }
                        else if (portal_type !== VALUES.portals.SALES_APP && !loginTime) { // if some user do not have last_login_time in db, then we will update the last_login_time in db and generate new token with the same time
                            // this case is used for users who are not salesperson and their session started before single device sing in feature deployment
                            loginTime = new Date();

                            await UserRoleSchema.updateOne(
                                { _id: userRoleDetails._id },
                                { [lastLoginDbField]: loginTime }
                            );
                        }

                        let sessionData = await UserSessionSchema.findOne(
                            {
                                status: VALUES.sessionStatus.ACTIVE,
                                access_token: request.headers.authorization,
                            }
                        );

                        if (sessionData) {
                            sessionData.status = VALUES.sessionStatus.EXPIRED;
                            await sessionData.save()
                        }

                        if (!userRoleDetails?.user_id) {
                            const errorMessage = "invalid_user_id"
                            const data = logErrorData()
                            const customError = new Error(errorMessage)

                            return response.handler.unauthorized(errorMessage, data, customError);
                        }

                        if (
                            sessionData &&
                            (stringifyObjectId(sessionData.user_id) !== stringifyObjectId(userRoleDetails.user_id._id))
                        ) {
                            sessionData.status = VALUES.sessionStatus.CLOSE;
                            await sessionData.save()

                            const errorMessage = "invalid_user_id"
                            const data = logErrorData({ sessionData })
                            const customError = new Error(errorMessage)

                            return response.handler.unauthorized(errorMessage, data, customError);
                        }

                        request.headers.userDetails = userRoleDetails.user_id;

                        const token = await UserAuthModal.generateAppAccessTokenFromRefreshToken(
                            request.headers.refreshtoken,
                            userRoleDetails,
                            loginTime,
                            deviceaccesstype,
                        );

                        const { user, tenant_customer } = jwt.verify(token, process.env.JWT_TOKEN_SECRET)

                        const userDetails = (
                            [
                                VALUES.portals.SALES_APP,
                                VALUES.portals.TENANT_PORTAL,
                                VALUES.portals.SUPERVISOR_APP
                            ].includes(portal_type)
                        )
                            ? user
                            : tenant_customer;

                        if (!userDetails) {
                            const errorMessage = "valid_user_not_found"

                            const data = logErrorData({
                                user,
                                tenant_customer,
                            })
                            const customError = new Error(errorMessage)

                            return response.handler.unauthorized(errorMessage, data, customError);
                        }

                        request.headers.updatedAccessToken = token;
                        request.headers.authorization = token; // Update authorization header for DataSync caching
                        response.setHeader("refreshed-access-token", token);
                        console.log('🔄 Updated authorization header with new access token for DataSync caching (JWT flow)');
                        console.log('🔄 New token preview:', token.substring(0, 50) + '...');

                        let newSession = new UserSessionSchema({
                            access_token: token,
                            status: VALUES.sessionStatus.ACTIVE,
                            total_session_time_in_sec: process.env.JWT_EXPIRE_IN_HOURS.split('h')[0] * 3600000,
                            user_id: userDetails._id,
                            device_token: request.headers.devicetoken,
                            device_type: request.headers.devicetype,
                            start_time: new Date(),
                            user_role_id: userRoleDetails._id,
                            tenant_id: userRoleDetails.tenant_id?._id,
                            collection_name: userRoleDetails.collection_name
                        });
                        await newSession.save();
                    }
                    catch (error) {
                        let errorMessage = error.message
                        const data = logErrorData()

                        switch (error.name) {
                            case "TokenExpiredError": {
                                errorMessage = "refresh_token_expired"
                                return response.handler.unauthorized(errorMessage, data, error);
                            }

                            case "invalid_user_id": {
                                errorMessage = "invalid_user_id"
                                return response.handler.unauthorized(errorMessage, data, error);
                            }

                            default: {
                                errorMessage = "token_regeneration_error"
                                return response.handler.unauthorized(errorMessage, data, error);
                            }
                        }
                    }
                    break;
                }

            case "JsonWebTokenError": {
                if (error.message === "invalid algorithm") {
                    checkCognitoToken = true;
                    break;
                }
                const errorMessage = "invalid_token"
                const data = logErrorData()
                return response.handler.unauthorized(errorMessage, data, error);
            }

            default:
                response.handler.serverError(error);
                break;
        }
    }

    if (checkCognitoToken) {
        request.headers.deviceTypeDetected = deviceTypeDetected.PORTAL;

        const jwkRes = await fetch(`https://cognito-idp.${VALUES.awsUserPoolRegion}.amazonaws.com/${VALUES.userPoolData.UserPoolId}/.well-known/jwks.json`)

        if (!jwkRes.ok) {
            return response.handler.unauthorized("VALIDATION.NOT_FOUND.COULD_NOT_FETCH");
        }
        let { keys } = await jwkRes.json();
        const pems = {}

        for (var i = 0; i < keys.length; i++) {
            //Convert each key to PEM
            let key_id = keys[i].kid;
            let modulus = keys[i].n;
            let exponent = keys[i].e;
            let key_type = keys[i].kty;
            let jwk = { kty: key_type, n: modulus, e: exponent };
            let pem = jwkToPem(jwk);
            pems[key_id] = pem;
        }
        let decodedJwt = jwt.decode(request.headers.authorization, { complete: true });

        if (!decodedJwt) {
            return response.handler.unauthorized("can_not_find_decodedJwt");
        }

        var kid = decodedJwt.header.kid;
        var pem = pems[kid];
        if (!pem) {
            return response.handler.unauthorized("can_not_find_pem");;
        }

        try {
            const payload = jwt.verify(request.headers.authorization, pem);
            request.headers.payload = payload;

            const [userDetails] = await Promise.all(
                [
                    UserSchema.findOne({ cognito_username: payload.sub }).lean(),
                    // UserRoleSchema.findById(request.headers.userroleid).populate({ path: "role_id" })
                ]);

            if (!userDetails) {
                return response.handler.notFound("valid_user_not_found");
            }
            request.headers.userDetails = userDetails;
        }
        catch (error) {
            switch (error.name) {
                case "TokenExpiredError":
                    let sessionData = await UserSessionSchema.findOne(
                        {
                            status: VALUES.sessionStatus.ACTIVE,
                            access_token: request.headers.authorization
                        }
                    );

                    const userRoleDetails = await UserRoleSchema
                        .findById(request.headers.userroleid)
                        .populate({ path: "role_id" })
                        .populate({ path: "user_id" })
                        .populate({ path: "tenant_id", select: 'services' })
                        .lean();

                    if (
                        !userRoleDetails ||
                        !userRoleDetails.is_active ||
                        userRoleDetails.is_deleted
                    ) {
                        return response.handler.unauthorized("can't_generate_new_token")
                    }

                    if (
                        sessionData &&
                        (stringifyObjectId(sessionData.user_id) !== stringifyObjectId(userRoleDetails.user_id._id))
                    ) {
                        const errorMessage = "invalid_user_id"
                        const data = logErrorData({
                            sessionData,
                            userRoleDetails,
                        })
                        const customError = new Error(errorMessage)

                        return response.handler.unauthorized(errorMessage, data, customError);
                    }

                    request.headers.sessionDetails = userRoleDetails;
                    request.headers.userDetails = userRoleDetails.user_id;
                    request.headers.tenantDetail = userRoleDetails.tenant_id;

                    const portal_type = userRoleDetails.role_id.portal_type;
                    request.headers.portalType = portal_type;

                    if (portal_type === VALUES.portals.SYSTEM_PORTAL) {
                        if (sessionData) {
                            sessionData.status = VALUES.sessionStatus.EXPIRED;
                            sessionData.end_time = new Date();
                            await sessionData.save();
                        }
                        return response.handler.unauthorized("VALIDATION.EXPIRED.TOKEN", { type: "TOKEN_EXPIRED" });
                    }
                    else if (
                        [
                            VALUES.portals.TENANT_PORTAL,
                            VALUES.portals.BRANCH_PORTAL,
                        ].includes(portal_type)
                    ) {
                        if (!request.headers.refreshtoken) {
                            if (sessionData) {
                                sessionData.status = VALUES.sessionStatus.EXPIRED;
                                sessionData.end_time = new Date();
                                await sessionData.save();
                            }
                            return response.handler.unauthorized("validation_not_found_valid_refresh_token")
                        }

                        try {
                            const tokens = await UserAuthModal.generateTokenFromRefreshToken(request.headers.refreshtoken);
                            const accessToken = tokens.AuthenticationResult?.AccessToken;
                            if (!accessToken) {
                                return response.handler.unauthorized("validation_not_found_valid_refresh_token")
                            }

                            //check that the refresh token belonged to original user....
                            const payload = jwt.verify(accessToken, pem);
                            request.headers.payload = payload;

                            const userDetails = await UserSchema.findOne({ cognito_username: payload.sub }).lean()

                            if (stringifyObjectId(userDetails._id) !== stringifyObjectId(userRoleDetails.user_id._id)) {
                                const errorMessage = "invalid_user_id"
                                const data = logErrorData({ userDetails })
                                const customError = new Error(errorMessage)

                                return response.handler.unauthorized(errorMessage, data, customError);
                            }

                            request.headers.updatedAccessToken = accessToken;
                            response.setHeader("refreshed-access-token", accessToken);

                            // Update the authorization header with new access token for caching
                            request.headers.authorization = accessToken;
                            console.log('🔄 Updated authorization header with new access token for DataSync caching');
                            console.log('🔄 New token preview:', accessToken.substring(0, 50) + '...');

                            if (sessionData) {
                                sessionData.status = VALUES.sessionStatus.EXPIRED;
                                await sessionData.save();
                            }

                            let newSession = new UserSessionSchema({
                                access_token: accessToken,
                                status: VALUES.sessionStatus.ACTIVE,
                                total_session_time_in_sec: tokens.AuthenticationResult.ExpiresIn,
                                user_id: userDetails._id,
                                device_token: request.headers.devicetoken,
                                device_type: request.headers.devicetype,
                                start_time: new Date(),
                                user_role_id: userRoleDetails._id,
                                tenant_id: userRoleDetails.tenant_id?._id,
                                collection_name: userRoleDetails.collection_name,
                            });
                            await newSession.save();

                            // Update cache with new access token for DataSync routes
                            if (request.originalUrl.includes('/dataSync') || request.headers.datasync === 'true') {
                                console.log('🔄 Updating cache with new access token after refresh...');
                                
                                // First, clean up old cache entries with the expired token
                                const oldToken = request.headers.authorization; // This was the expired token
                                datasyncCache.updateCacheWithNewToken(oldToken, accessToken, request.headers);
                                
                                // Create new cache key with updated authorization header
                                const newCacheKey = datasyncCache.createCacheKey({
                                    authorization: accessToken, // Use the new token
                                    devicetoken: request.headers.devicetoken,
                                    refreshtoken: request.headers.refreshtoken,
                                    userid: request.headers.userroleid
                                });
                                
                                // Prepare updated cache data
                                const updatedCacheData = {
                                    sessionDetails: request.headers.sessionDetails,
                                    portalType: request.headers.portalType,
                                    tenantDetail: request.headers.tenantDetail,
                                    userDetails: request.headers.userDetails,
                                    deviceTypeDetected: request.headers.deviceTypeDetected,
                                    originalHeaders: {
                                        authorization: accessToken, // New token
                                        devicetoken: request.headers.devicetoken,
                                        refreshtoken: request.headers.refreshtoken,
                                        userroleid: request.headers.userroleid
                                    },
                                    cachedAt: Date.now(),
                                    expiresIn: tokens.AuthenticationResult.ExpiresIn || 600,
                                    isRefreshed: true,
                                    originalToken: oldToken
                                };
                                
                                // Set the updated cache entry
                                datasyncCache.setCache(newCacheKey, updatedCacheData, tokens.AuthenticationResult.ExpiresIn || 600);
                                console.log('✅ Cache updated with new access token');
                            }
                        }
                        catch (error) {
                            if (sessionData) {
                                sessionData.status = VALUES.sessionStatus.EXPIRED;
                                sessionData.end_time = new Date();
                                await sessionData.save();
                            }
                            return response.handler.unauthorized("validation_not_found_valid_refresh_token")
                        }
                    }
                    else {
                        if (sessionData) {
                            sessionData.status = VALUES.sessionStatus.CLOSE;
                            sessionData.end_time = new Date();
                            await sessionData.save();
                        }
                        return response.handler.unauthorized("validation_not_found_valid_session_details");
                    }
                    break;

                case "JsonWebTokenError": {
                    return response.handler.unauthorized("invalid_signature");
                }

                default: {
                    return response.handler.unauthorized(error.message);
                }
            }
        }
    }

    if (
        request.headers.userroleid &&
        (!userRoleDetails || userRoleDetails.is_deleted)
    ) {
        const errorMessage = "invalid_user_role_id"
        const data = logErrorData()
        const customError = new Error(errorMessage)

        return response.handler.unauthorized(errorMessage, data, customError);
    }

    if (
        request.headers.userroleid &&
        stringifyObjectId(request.headers.userDetails._id) !== stringifyObjectId(userRoleDetails.user_id._id)
    ) {
        const errorMessage = "invalid_user_id"
        const data = logErrorData()
        const customError = new Error(errorMessage)

        return response.handler.unauthorized(errorMessage, data, customError);
    }

    // Cache successful authentication results for DataSync routes
    if ((request.originalUrl.includes('/dataSync') || request.headers.datasync === 'true') && 
        request.headers.sessionDetails && 
        request.headers.userDetails ) {
        
        console.log('💾 Attempting to cache DataSync authentication result...');
        console.log('🔍 Current authorization header:', request.headers.authorization ? 'Present' : 'Missing');
        console.log('🔍 Authorization header preview:', request.headers.authorization ? request.headers.authorization.substring(0, 50) + '...' : 'N/A');
        
        const cacheKey = datasyncCache.createCacheKey({
            authorization: request.headers.authorization,
            devicetoken: request.headers.devicetoken,
            refreshtoken: request.headers.refreshtoken,
            userid: request.headers.userroleid
        });
        const accessToken = request.headers.authorization;
        const refreshToken = request.headers.refreshtoken;
        
        // Calculate cache expiry based on token expiry
        const cacheExpiry = datasyncCache.calculateCacheExpiry(accessToken, refreshToken, 600); // 10 minutes default
        
        // Prepare data to cache
        const cacheData = {
            sessionDetails: request.headers.sessionDetails,
            portalType: request.headers.portalType,
            tenantDetail: request.headers.tenantDetail,
            userDetails: request.headers.userDetails,
            deviceTypeDetected: request.headers.deviceTypeDetected,
            originalHeaders: {
                authorization: request.headers.authorization,
                devicetoken: request.headers.devicetoken,
                refreshtoken: request.headers.refreshtoken,
                userroleid: request.headers.userroleid
            },
            cachedAt: Date.now(),
            expiresIn: cacheExpiry
        };
        
        // Cache the authentication result
        console.log('🔍 Caching auth result with key:', cacheKey);
        console.log('🔍 Cache data:', cacheData);   
        console.log('🔍 Cache expiry:', cacheExpiry);
        datasyncCache.setCache(cacheKey, cacheData, cacheExpiry);
        
        console.log(`✅ DataSync Auth Cached for ${cacheExpiry}s:`, request.originalUrl);
    } else {
        if (request.originalUrl.includes('/datasync') || request.headers.datasync === 'true') {
            console.log('❌ Cannot cache DataSync auth - missing required data:');
            console.log('   - sessionDetails:', !!request.headers.sessionDetails);
            console.log('   - userDetails:', !!request.headers.userDetails);
            console.log('   - URL:', request.originalUrl);
        }
    }

    next();
};
