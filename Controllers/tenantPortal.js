const path = require('path');
const excelJS = require("exceljs");
const difference = require('lodash.difference')
const differenceWith = require('lodash.differencewith')

const {
    VALUES,
    LANGUAGE,
    BUCKET_TYPE,
    FILE_PATH,
    ENTITY_STATUS,
    BRANCH_LIST_TYPE,
    TENANT_ALLOW_VALIDATION,
    DATA_SHEET,
    STATUS_CODES,
    PRIMITIVE_ROLES,
    TENANT_ADVANCE_LIMIT_KEYS,
    FALSELY_VALUES,
    PROFILE_TYPE_ENUM,
    SETTINGS,
    QR_CODE_TYPE
} = require('../Configs/constants');

const CommonModal = new (require('../Models/common'))();
const CommonController = new (require("../Controllers/common/common"))();

const RoleModal = new (require('../Models/roles'))();
const UserAuthModal = new (require('../Models/auth'))();
const TenantPortalModal = new (require("../Models/tenantPortal"))();
const SystemPortalModal = new (require("../Models/systemPortal"))();

const DataSheetModel = new (require("../Models/DataSheetModel"))
const CustomerDataSheetModel = require('../Models/CustomerDataSheetModel')
const CustomerPaymentTermModel = new (require("../Models/RewardProgram/CustomerPaymentTermModel"))();
const InternalServiceModal = new (require("../Models/InternalServiceModal"))();

const RewardProgramPointController = new (require("../Controllers/RewardProgram/RewardProgramPointController"))()

const EncryptionHandler = new (require("../Configs/encrypt"))();

const Api = new (require("../Utils/Api"))()
const sendFailureEmail = require("../Middleware/SendFailureEmail").default

const FileUpload = require('../Configs/awsUploader').S3Upload;
const fileUpload = new FileUpload();

const {
    generateOtp,
    httpService,
    removeAllWhitespace,
    stringifyObjectId,
    toLeanOption,
    excludeProjections,
} = require('../Utils/helpers');

class TenantPortalController {
    async addTenantBranchPortalUser(req, res) {
        try {

            const body = req.body;
            body.isActive = body.isActive === "true" ? true : false;
            body.allowPriceChange = body.allowPriceChange === "true" ? true : false;
            body.tenantId = Number(body.tenantId);
            body.notifications = body.notifications.map(n => JSON.parse(n))
            body.isActive = body.isActive; // Dec 30 users and customer limit validation
            /*if (body.isActive) {
                const defaultTenantSetting = await SystemPortalModal.getTenantDetailsById(body.tenantId)
                let tenantAdvanceLimit = defaultTenantSetting.advance_limit.find(data => data.key === 'NUMBER_OF_USERS');
                const allowUsers = tenantAdvanceLimit.allowance
                const activeUsers = await TenantPortalModal.allowNewValidation(body.tenantId, TENANT_ALLOW_VALIDATION.USER)
                if (allowUsers <= activeUsers) {
                    body.isActive = false;
                }
            }*/ // Dec 30 users and customer limit validation
            let userDetails;
            let message;
            let userRecovered = false;
            let newUser = false;
            const [userWithMobile, userWithEmail, roleInfo, tenantInfo] = await Promise.all([
                UserAuthModal.findUserByMobileNumber(body.mobileNumber, body.countryCode),
                UserAuthModal.findUserByEmail(body.email),
                RoleModal.getRoleFromRoleID(body.roleId),
                SystemPortalModal.getTenantById(body.tenantId),
            ]);

            if (!roleInfo || roleInfo.portal_type === VALUES.portals.SYSTEM_PORTAL || roleInfo.name.toLowerCase() === "tenant owner") {
                return res.handler.badRequest("validation_invalid_role_type");
            }

            if (!tenantInfo || tenantInfo.is_deleted) {
                return res.handler.notFound("validation_not_found_tenant")
            }
            let updateUserDetails = {};
            let hasExtraProfile = false;
            if (userWithMobile) {

                if (userWithEmail && userWithMobile._id.toString() !== userWithEmail._id.toString()) {
                    return res.handler.custom(STATUS_CODES.CONFLICT, 'validation_exists_email');
                }
                userDetails = userWithMobile;
                const otherTenantProfile = await UserAuthModal.getUserRoleWithPopulation(
                    // { $and: [{ tenant_id: { $ne: tenantInfo._id } }, { tenant_id: { $ne: null } }], is_deleted: false, user_id: userDetails._id },
                    { tenant_id: { $ne: null }, is_deleted: false, user_id: userDetails._id }, // if we make one profile(with particular user_id) in any tenant need to responde with 400 response code
                    { _id: 1, tenant_id: 1 },
                );
                if (otherTenantProfile) {
                    hasExtraProfile = true;
                    // return res.handler.custom(STATUS_CODES.CONFLICT, "validation_exists_user");
                }
                updateUserDetails = {
                    firstName: body.firstName,
                    lastName: body.lastName,
                    previousEmail: userDetails.cognito_username,
                    email: body.email,
                    countryCode: body.countryCode,
                    mobileNumber: body.mobileNumber
                }
                // update user details in cognito
                // const result = await UserAuthModal.updateCognitoUserNameAndEmail(updateUserDetails, req.headers);
                userDetails.first_name = body.firstName;
                userDetails.last_name = body.lastName;
                userDetails.email = body.email;
                userDetails.updated_by = req.headers.userDetails._id;
                userDetails.country_code = body.countryCode;
                userDetails.mobile_number = body.mobileNumber;
                userDetails.is_active = true;
                if (userDetails.is_deleted) {
                    userDetails.is_deleted = false
                    userRecovered = true
                }
                //TODO: recover the user role based on role id provided in body
                // await userWithMobile.save()  // need to save user after success
                userDetails = userWithMobile;
                message = "restored_user"

            } else if (userWithEmail) {
                if (userWithMobile && userWithMobile._id.toString() !== userWithEmail._id.toString()) {
                    return res.handler.custom(STATUS_CODES.CONFLICT, 'validation_exists_mobile_number',);
                }
                userDetails = userWithEmail;
                const otherTenantProfile = await UserAuthModal.getUserRoleWithPopulation(
                    // { $and: [{ tenant_id: { $ne: tenantInfo._id } }, { tenant_id: { $ne: null } }], is_deleted: false, user_id: userDetails._id },
                    { tenant_id: { $ne: null }, is_deleted: false, user_id: userDetails._id }, // if we make one profile(with particular user_id) in any tenant need to responde with 400 response code
                    { _id: 1 },
                );
                if (otherTenantProfile) {
                    hasExtraProfile = true;
                    // return res.handler.custom(STATUS_CODES.CONFLICT, "validation_exists_user");
                }

                updateUserDetails = {
                    firstName: body.firstName,
                    lastName: body.lastName,
                    previousEmail: userWithEmail.cognito_username,
                    email: body.email,
                    countryCode: body.countryCode,
                    mobileNumber: body.mobileNumber
                }
                // const result = await UserAuthModal.updateCognitoUserNameAndEmail(updateUserDetails, req.headers);
                //TODO: need to create (unique_mobile_number => country_code + mobile_number) && email as unique indexes
                userDetails.first_name = body.firstName;
                userDetails.last_name = body.lastName;
                userDetails.email = body.email;
                userDetails.updated_by = req.headers.userDetails._id;
                userDetails.country_code = body.countryCode;
                userDetails.mobile_number = body.mobileNumber;
                userDetails.is_active = true;
                if (userDetails.is_deleted) {
                    userDetails.is_deleted = false
                    userRecovered = true
                }

                // await userWithEmail.save();
                userDetails = userWithEmail;
                message = "RESTORED.USER";
            } else {
                //TODO: need to create new user and assign it to userDetails
                newUser = true;
                req.body.password = "66G1!d8LviS02x@5i$7"
                const user = await UserAuthModal.signup(req.body);
                const cognitoData = await UserAuthModal.creatUserInUserPool(req.body);
                user.cognito_username = cognitoData.userSub;
                const otp = generateOtp();
                user.forgot_password_otp = otp;
                user.otp_verification_time = new Date();
                user.otp_verification_time_limit = VALUES.PASSWORD_REST_OR_CREATE_DURATION;
                // save user in DB
                await user.save();
                userDetails = user;

                // await UserAuthModal.sendEMailVerificationLink(req.body.email, otp, user._id.toString(), "Reset Password");
                await Promise.all([UserAuthModal.adminConfirmSignUp(req.body), UserAuthModal.verifyUserDetails(req.body)]);
                // TODO: need to upload image in S3
            }

            if (req.body.profilePic) {

                if (!newUser && userDetails.profile_pic && req.body.profilePic) {
                    await fileUpload.deleteFile(FILE_PATH.USER_PROFILE, userDetails.profile_pic);
                }

                let fileNames = await fileUpload.uploadFiles(FILE_PATH.USER_PROFILE, req.body.profilePic);
                userDetails.profile_pic = fileNames.data[0].fileName;
                await userDetails.save();
            }

            switch (body.portalType) {
                case VALUES.portals.TENANT_PORTAL: // when the request is fired from
                    // ---------- VALIDATORS ------------- //
                    break;

                case VALUES.portals.BRANCH_PORTAL:
                    // ---------- VALIDATORS ------------- //
                    if (roleInfo.name.toLowerCase() !== "sales person") {
                        return res.handler.validationError("validation_invalid_role_type")
                    }

                    break;
            }

            const appSetting = await TenantPortalModal.tenantAppSettingExist(body.tenantId)

            const {
                hide_out_of_stock_product = false,
                preferred_language = "en",
                price_change = true,
            } = appSetting || {}

            const settings = {
                "out_of_stock": {
                    "visible": !hide_out_of_stock_product,
                    "searchable": !hide_out_of_stock_product,
                },
                "price_change": price_change,
                "preferred_language": preferred_language,
                // by default it will be the default price list type
                // "default_master_price_id": "",
            }
            let responseMessage = "added_user";
            let userRole;
            switch (roleInfo.portal_type) {
                case VALUES.portals.TENANT_PORTAL:
                    let tenantRoles = await RoleModal.getTenantPortalRoleByTenantId()
                    tenantRoles = tenantRoles.map(tr => tr._id);
                    userRole = await TenantPortalModal.findExistingTenantPortalProfile(body.tenantId, userDetails._id, tenantRoles);
                    if (userRole) {
                        if (userRole.is_deleted && userRole.role_id.name.toLowerCase() !== "tenant owner") {
                            userRole.is_deleted = false;
                            userRole.is_active = body.isActive;
                            userRole.role_id = roleInfo._id;
                            userRole.allow_price_change = body.allowPriceChange;
                            userRole.allow_all_permission = true;
                            hasExtraProfile ? userRole.profile_type = PROFILE_TYPE_ENUM.EXTRA_PROFILE : null;
                            responseMessage = "restored_user"
                        } else {
                            return res.handler.badRequest("validation_invalid_assign_role");
                        }
                    } else {
                        //TODO: create new user_role record as non deleted and according to isActive
                        body.user_id = userDetails._id;
                        body.tenant_id = tenantInfo._id;
                        body.role_id = roleInfo._id;
                        body.is_active = body.isActive;
                        body.allow_all_permission = true
                        // if(roleInfo.name.toLowerCase() === "admin") {
                        // } else {
                        //     body.allow_all_permission = false
                        // }
                        userRole = await TenantPortalModal.createTenantPortalProfile(body, req.headers);
                        settings['user_role_id'] = userRole._id;
                        settings['tenant_id'] = body.tenantId;
                        settings['_id'] = `${body.tenantId}_${userRole._id}`;
                        const userRoleSettings = await TenantPortalModal.createUserRoleSetting(settings)
                        await userRoleSettings.save();
                        hasExtraProfile ? userRole.profile_type = PROFILE_TYPE_ENUM.EXTRA_PROFILE : null;
                    }

                    //Allow posting of voucher receipts option to admin profile. Only owner can give this permission
                    if (body.allowPostingVoucherReceipts !== undefined) {
                        const userRoleName = req.headers.sessionDetails?.role_id.name

                        if (userRoleName === PRIMITIVE_ROLES.TENANT_OWNER && roleInfo.name === PRIMITIVE_ROLES.TENANT_ADMIN) {
                            userRole.allow_posting_voucher_receipts = body.allowPostingVoucherReceipts
                        }
                        else {
                            return res.handler.validationError("cant_update_allow_posting_voucher_receipts")
                        }
                    }

                    //Don't sent any notification to Accountant & Contributor
                    if (
                        [
                            PRIMITIVE_ROLES.ACCOUNTANT,
                            PRIMITIVE_ROLES.CONTRIBUTOR,
                        ].includes(roleInfo.name)
                    ) {
                        userRole.notifications.forEach(notification => {
                            notification.allow_push_notification = false
                            notification.allow_sms_notification = false
                            notification.allow_email_notification = false
                        })
                    }

                    await userRole.save();
                    break;

                case VALUES.portals.BRANCH_PORTAL:
                    var [branchRoles, branchInfo] = await Promise.all([
                        RoleModal.getStandardBranchPortalRoles(),
                        TenantPortalModal.findBranchById(body.branchId)
                    ]);
                    if (!branchInfo || branchInfo.is_deleted || branchInfo.tenant_id !== body.tenantId) {
                        return res.handler.notFound("validation_not_found_branch");
                    }

                    var roleIndex = branchRoles.findIndex(r => r.id === roleInfo.id)
                    if (roleIndex === -1) {
                        return res.handler.notFound("NOT_FOUND.ROLE")
                    }

                    userRole = await TenantPortalModal.findExistingBranchPortalProfile(body.tenantId, branchInfo._id, userDetails._id, branchRoles.map(r => r._id));
                    if (roleInfo.name.toLowerCase() === "branch manager") {
                        if (userRole) {
                            // check user has already access to the given branch or not, if deleted, then update the user_id and recover it
                            if (userRole.is_deleted) {
                                userRole.is_deleted = false;
                                userRole.is_active = body.isActive;
                                userRole.allow_price_change = body.allowPriceChange;
                                userRole.notifications = body.notifications;
                                userRole.collection_name = "users";
                                hasExtraProfile ? userRole.profile_type = PROFILE_TYPE_ENUM.EXTRA_PROFILE : null;
                                // userRole.settings = settings
                                await userRole.save();
                                responseMessage = "restored_user"
                            } else {
                                return res.handler.badRequest("validation_exists_assigned_role_branch");
                            }
                        } else {
                            body.user_id = userDetails._id;
                            userRole = await TenantPortalModal.createBranchManager(body, req.headers);
                            hasExtraProfile ? userRole.profile_type = PROFILE_TYPE_ENUM.EXTRA_PROFILE : null;
                            await userRole.save();
                        }
                    } else {
                        return res.handler.badRequest("not_supported_role");
                    }
                    break;

                case VALUES.portals.SALES_APP:
                    {

                        var [branchInfo, salesAppRoles] = await Promise.all([
                            TenantPortalModal.findBranchById(body.branchId || ""),
                            RoleModal.getStandardSalesAppRoles()
                        ]);
                        const roleNameLower = roleInfo.name.toLowerCase();
                        if (roleNameLower === "sales person" && (!branchInfo || branchInfo.is_deleted || branchInfo.tenant_id !== body.tenantId)) {
                            return res.handler.notFound("validation_not_found_branch");
                        }
                        var roleIndex = salesAppRoles.findIndex(r => r.id === roleInfo.id)
                        if (roleIndex === -1) {
                            return res.handler.notFound("validation_not_found_role")
                        }

                        userRole = await TenantPortalModal.findExistingSalesAppProfile(body.tenantId, null, userDetails._id, salesAppRoles.map(r => r._id));

                        if (roleNameLower === "sales person") {
                            // Supervisor is not mandatory
                            if (body.supervisorId) {
                                const supervisorRoleInfo = await TenantPortalModal.findSuperVisorWithUserId(body.supervisorId, body.tenantId, body.branchId);
                                if (!supervisorRoleInfo || supervisorRoleInfo.is_deleted) {
                                    return res.handler.notFound("validation_not_found_supervisor")
                                }
                                if (!supervisorRoleInfo.user_id || supervisorRoleInfo.user_id.is_deleted) {
                                    return res.handler.notFound("validation_not_found_supervisor")
                                }
                            }


                            if (userRole) {
                                if (userRole.is_deleted) {
                                    userRole.is_deleted = false;
                                    userRole.is_active = body.isActive;
                                    userRole.allow_price_change = body.allowPriceChange;
                                    userRole.notifications = body.notifications;
                                    userRole.supervisor_id = body.supervisorId;
                                    userRole.branch_id = body.branchId;
                                    userRole.allow_all_permission = false;
                                    userRole.collection_name = "users";
                                    // userRole.settings = settings
                                    hasExtraProfile ? userRole.profile_type = PROFILE_TYPE_ENUM.EXTRA_PROFILE : null;
                                    await userRole.save();
                                    responseMessage = "restored_user"
                                }
                                else {
                                    return res.handler.badRequest("validation_exists_assigned_role_branch");
                                }
                            }
                            else {
                                body.user_id = userDetails._id;
                                userRole = await TenantPortalModal.createSalesPerson(body, req.headers);
                                settings['user_role_id'] = userRole._id;
                                settings['tenant_id'] = body.tenantId;
                                settings['_id'] = `${body.tenantId}_${userRole._id}`;
                                // userRole.settings = settings
                                const userRoleSettings = await TenantPortalModal.createUserRoleSetting(settings)
                                await userRoleSettings.save();
                                hasExtraProfile ? userRole.profile_type = PROFILE_TYPE_ENUM.EXTRA_PROFILE : null;
                                await userRole.save();
                            }
                        }
                        break;
                    }

                case VALUES.portals.SUPERVISOR_APP:
                    {
                        const SupervisorRole = await RoleModal.getRoleByFilter({ name: PRIMITIVE_ROLES.SUPERVISOR }, { _id: 1 });
                        userRole = await TenantPortalModal.findExistingSalesAppProfile(body.tenantId, null, userDetails._id, [SupervisorRole._id]);
                        if (userRole) {
                            if (userRole.is_deleted) {
                                userRole.is_deleted = false;
                                userRole.is_active = body.isActive;
                                userRole.allow_price_change = body.allowPriceChange;
                                userRole.notifications = body.notifications;
                                userRole.supervisor_id = body.supervisorId;
                                userRole.branch_id = null; // https://www.bugherd.com/projects/304712/tasks/83 ==> supervisor is not linked with any branch
                                userRole.allow_all_permission = true; // https://www.bugherd.com/projects/304712/tasks/83 ==> supervisor is not linked with any branch
                                userRole.collection_name = "users";
                                await userRole.save();
                                hasExtraProfile ? userRole.profile_type = PROFILE_TYPE_ENUM.EXTRA_PROFILE : null;
                                responseMessage = "restored_user"
                            } else {
                                return res.handler.badRequest("validation_exists_assigned_role_branch");
                            }

                        } else {
                            body.user_id = userDetails._id;
                            userRole = await TenantPortalModal.createSupervisor(body, req.headers);
                            hasExtraProfile ? userRole.profile_type = PROFILE_TYPE_ENUM.EXTRA_PROFILE : null;
                            await userRole.save();
                        }
                        break;
                    }
            }
            //update user details in mongoDB and cognito
            if (!newUser) {
                // only update db after successfully updating in details in cognito
                await UserAuthModal.updateCognitoUserNameAndEmail(updateUserDetails, req.headers);
                await userDetails.save();
            } else {
                await UserAuthModal.sendWelcomePortalUser(userDetails);
            }
            return res.handler.success(responseMessage, { _id: userRole._id });
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async listTenantAndBranchUser(req, res) {
        try {
            const body = req.query;
            body.tenantId = Number(body.tenantId);
            const sessionDetails = req.headers.sessionDetails;
            // if(!sessionDetails?.user_role_id || !sessionDetails.user_role_id.role_id) {
            //     return res.handler.badRequest("validation_invalid_token_user_role");
            // }
            let defaultRoles;
            switch (sessionDetails.role_id.portal_type) {
                case VALUES.portals.SYSTEM_PORTAL:
                    // ----- VALIDATORS ----- //
                    // TODO: for account manager he should not be able see non assigned portal
                    defaultRoles = await RoleModal.getTenantAndBranchPortal();
                    defaultRoles = defaultRoles.map(r => r._id);
                    break;

                case VALUES.portals.TENANT_PORTAL:
                    // ----- VALIDATORS ----- //
                    //DISABLED, AFTER LINKED TENANT CHANGES
                    // if (sessionDetails.tenant_id !== body.tenantId) {
                    //     return res.handler.badRequest("bad_request_tenant_not_matched")
                    // }
                    defaultRoles = await RoleModal.getTenantAndBranchPortal();
                    defaultRoles = defaultRoles.map(r => r._id);
                    break;

                case VALUES.portals.BRANCH_PORTAL:
                    //DISABLED, AFTER LINKED TENANT CHANGES
                    // if (sessionDetails.tenant_id !== body.tenantId) {
                    //     return res.handler.badRequest("bad_request_tenant_not_matched")
                    // }

                    if (sessionDetails.branch_id.toString() !== body.branchId) {
                        return res.handler.badRequest("bad_request_branch_not_matched")
                    }
                    defaultRoles = await RoleModal.getStandardBranchPortalRoles();
                    // defaultRoles = await RoleModal.getRolesWithFilter({ name: { $in: ["Branch Manager", "Sales Person"] } }, { _id: 1 });
                    defaultRoles = defaultRoles.map(r => r._id);
                    break;
            }

            body.defaultRoles = defaultRoles;
            const result = await TenantPortalModal.findTenantAndBranchUsers(body, req.headers);

            return res.handler.success(null, result);

        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async changeTenantUserRoleStatus(req, res) {
        try {
            const {
                userRoleIds,
                status,
                tenantId,
            } = req.body;

            let tenantPortalRoles = await RoleModal.getTenantAndBranchPortal();
            tenantPortalRoles = tenantPortalRoles.map(spr => spr._id);

            await TenantPortalModal.updateTenantUserRoleStatus(userRoleIds, tenantPortalRoles, status, req.headers)

            let message;
            switch (status) {
                case ENTITY_STATUS.ACTIVE:
                    message = "updated_activated_user_roles"
                    break;

                default: {
                    message = "updated_in_activated_user_roles"

                    const salesPersonRoleId = await TenantPortalModal.getSalesPersonRole(
                        { _id: 1 },
                        toLeanOption,
                    )

                    const salespersonUserRoles = await TenantPortalModal.getTenantUsers(
                        {
                            "_id": {
                                "$in": userRoleIds,
                            },
                            "tenant_id": tenantId,
                            "role_id": salesPersonRoleId,
                            "is_deleted": false,
                        },
                        {
                            "_id": 1,
                        },
                        toLeanOption,
                    )

                    if (salespersonUserRoles.length) {
                        /**
                         * If salesperson(s) status changed to inactive,
                         * then remove salesperson(s) from work time config.
                         */
                        try {
                            const salespersonIds = salespersonUserRoles.map(element => element._id)

                            await InternalServiceModal.removeSalespersonFromTracking(
                                req,
                                {
                                    tenantId,
                                    salespersonIds,
                                }
                            )
                        }
                        catch (e) {
                            logger.error(e)
                        }
                    }
                    break;
                }
            }
            return res.handler.success(message);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getTenantSupervisors(req, res) {
        try {
            const roleId = await TenantPortalModal.findSupervisorRole();
            const supervisorsProfiles = await TenantPortalModal.getTenantUsersWithRoleId(req.query.tenantId, roleId._id);
            return res.handler.success(null, supervisorsProfiles);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getTenantSalesperson(req, res) {
        try {
            const {
                tenantId,
                branchId,
                isActive
            } = req.query

            const roleId = await TenantPortalModal.getSalesPersonRole()

            const salespersonProfiles = await TenantPortalModal.getTenantUsersWithRoleId(
                tenantId,
                roleId._id,
                branchId,
                isActive,
            )

            res.handler.success(null, salespersonProfiles)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async usersDetails(req, res) {
        try {
            const { tenantId, ids } = req.body;
            const filter = { tenant_id: tenantId, is_deleted: false, _id: { $in: ids } };
            const project = {
                is_deleted: 0,
                allow_all_permission: 0,
                role_id: 0,
                allow_price_change: 0,
                created_by: 0,
                updated_by: 0,
                created_at: 0,
                updated_at: 0,
                settings: 0,
                supervisor_id: 0,
                notifications: 0,
                __v: 0
            }

            const populationArray = [
                {
                    path: "user_id",
                    select: "_id first_name last_name is_active is_deleted"
                }
            ]
            const usersDetailsWithIds = await TenantPortalModal.usersDetailsWithIds(filter, project, populationArray, { lean: true });

            const updatedData = usersDetailsWithIds.map(item => {
                const {
                    tenant_id,
                    profile_pic,
                } = item

                const profilePicUrl = CommonController.getProfileImageUrl(tenant_id) + profile_pic;
                const profileThumbPicUrl = CommonController.getProfileThumbImageUrl(tenant_id) + profile_pic;

                const response = {
                    ...item,
                }

                if (profile_pic) {
                    response["profile_pic"] = profilePicUrl
                    response["profile_thumbnail_pic"] = profileThumbPicUrl
                }
                return response
            });

            return res.handler.success(null, updatedData);
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async getTenantBranchPortalUser(req, res) {
        try {
            const userRole = await TenantPortalModal.getTenantBranchPortalUserById(
                req.query.userRoleId,
                excludeProjections,
                toLeanOption,
            );

            if (!userRole || userRole.is_deleted) {
                return res.handler.notFound("user_not_found");
            }
            const {
                tenant_id,
                profile_pic,
            } = userRole

            if (profile_pic) {
                userRole.profile_pic = CommonController.getProfileImageUrl(tenant_id) + profile_pic;
                userRole.profile_thumbnail_pic = CommonController.getProfileThumbImageUrl(tenant_id) + profile_pic;
            }
            return res.handler.success(null, userRole);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    // async updateUserRole(req, res) {
    //     try {
    //         const { preferred_language } = req.body;
    //         const userRoleId = req.headers?.sessionDetails?._id;

    //         if (!userRoleId) {
    //             return res.handler.notFound("validation_not_found_user_role");
    //         }

    //         const userRoleInfo = await TenantPortalModal.getOnlyUserRoleById(userRoleId);
    //         if (!userRoleInfo?.is_deleted === false) {
    //             return res.handler.notFound("validation_not_found_user_role");
    //         }

    //         await TenantPortalModal.updateUserRole(userRoleId, preferred_language);
    //         return res.handler.success();
    //     } catch (error) {
    //         return res.handler.serverError(error);
    //     }
    // }

    async updateTenantBranchPortalUser(req, res) {
        try {

            const body = req.body;

            let userDetails;
            let newUser = false;
            const [userWithMobile, userWithEmail, roleInfo, tenantInfo, userRoleInfo] = await Promise.all([
                UserAuthModal.findUserByMobileNumber(body.mobileNumber, body.countryCode),
                UserAuthModal.findUserByEmail(body.email),
                RoleModal.getRoleFromRoleID(body.roleId),
                SystemPortalModal.getTenantById(body.tenantId),
                TenantPortalModal.getOnlyUserRoleById(req.body.userRoleId)
            ]);

            if (!userRoleInfo || userRoleInfo.is_deleted) {
                return res.handler.notFound("validation_not_found_user_role");
            }

            if (userRoleInfo.tenant_id !== body.tenantId) {
                return res.handler.badRequest("tenant_id_mismatched");
            }

            const currentRoleInfo = await RoleModal.getRoleFromRoleID(userRoleInfo.role_id);

            if (currentRoleInfo.portal_type === VALUES.portals.SYSTEM_PORTAL) {
                return res.handler.badRequest("validation_invalid_role_type");
            }

            if (!roleInfo || roleInfo.portal_type === VALUES.portals.SYSTEM_PORTAL) {
                return res.handler.badRequest("validation_invalid_role_type");
            }

            if ((currentRoleInfo.name.toLowerCase() === "tenant owner" && roleInfo.id !== currentRoleInfo.id)) {
                return res.handler.badRequest("validation_invalid_role_type");
            }

            if (!tenantInfo || tenantInfo.is_deleted) {
                return res.handler.notFound("validation_not_found_tenant")
            }

            let updateUserDetails = {};
            if (userWithMobile) {

                if (userWithEmail && userWithMobile._id.toString() !== userWithEmail._id.toString()) {
                    return res.handler.custom(STATUS_CODES.CONFLICT, 'validation_exists_email');
                }

                userDetails = userWithMobile;
                // const otherTenantProfile = await UserAuthModal.getUserRoleWithPopulation(
                //     { $and: [{ tenant_id: { $ne: tenantInfo._id } }, { tenant_id: { $ne: null } }], is_deleted: false, user_id: userDetails._id },
                //     { _id: 1 },
                // );
                // if (otherTenantProfile) {
                //     return res.handler.custom(STATUS_CODES.CONFLICT, "validation_exists_user");
                // }

                userDetails = userWithMobile;

                updateUserDetails = {
                    firstName: body.firstName,
                    lastName: body.lastName,
                    previousEmail: userDetails.cognito_username,
                    email: body.email,
                    countryCode: body.countryCode,
                    mobileNumber: body.mobileNumber
                }
                // update user details in cognito
                // const result = await UserAuthModal.updateCognitoUserNameAndEmail(updateUserDetails, req.headers);
                userDetails.first_name = body.firstName;
                userDetails.last_name = body.lastName;
                userDetails.email = body.email;
                userDetails.updated_by = req.headers.userDetails._id;
                userDetails.country_code = body.countryCode;
                userDetails.mobile_number = body.mobileNumber;
                userDetails.is_active = true;
                if (!userDetails.is_deleted) {
                    userDetails.is_deleted = false;
                }
                // await userWithMobile.save()

            } else if (userWithEmail) {
                if (userWithMobile && userWithMobile._id.toString() !== userWithEmail._id.toString()) {
                    return res.handler.custom(STATUS_CODES.CONFLICT, 'VALIDATION.EXISTS.EMAIL');
                }
                userDetails = userWithEmail;
                // const otherTenantProfile = await UserAuthModal.getUserRoleWithPopulation(
                //     { $and: [{ tenant_id: { $ne: tenantInfo._id } }, { tenant_id: { $ne: null } }], is_deleted: false, user_id: userDetails._id },
                //     { _id: 1 },
                // );
                // if (otherTenantProfile) {
                //     return res.handler.custom(STATUS_CODES.CONFLICT, "validation_exists_user");
                // }
                updateUserDetails = {
                    firstName: body.firstName,
                    lastName: body.lastName,
                    previousEmail: userDetails.cognito_username,
                    email: body.email,
                    countryCode: body.countryCode,
                    mobileNumber: body.mobileNumber
                }
                // const result = await UserAuthModal.updateCognitoUserNameAndEmail(updateUserDetails, req.headers);
                userDetails.first_name = body.firstName;
                userDetails.last_name = body.lastName;
                userDetails.email = body.email;
                userDetails.updated_by = req.headers.userDetails._id;
                userDetails.country_code = body.countryCode;
                userDetails.mobile_number = body.mobileNumber;
                userDetails.is_active = true;
                if (!userDetails.is_deleted) {
                    userDetails.is_deleted = false
                }

            } else {
                newUser = true;
                req.body.password = "66G1!d8LviS02x@5i$7"
                const user = await UserAuthModal.signup(req.body);
                const cognitoData = await UserAuthModal.creatUserInUserPool(req.body);
                user.cognito_username = cognitoData.userSub;
                const otp = generateOtp();
                user.forgot_password_otp = otp;
                user.otp_verification_time = new Date();
                user.otp_verification_time_limit = VALUES.PASSWORD_REST_OR_CREATE_DURATION;
                // save user in DB
                await user.save();
                userDetails = user;
                await UserAuthModal.sendEMailVerificationLink(req.body.email, otp, user._id.toString(), "Reset Password");
                await Promise.all([UserAuthModal.adminConfirmSignUp(req.body), UserAuthModal.verifyUserDetails(req.body)]);
            }
            // update the user id for this user role

            const userRoleDetails = req.headers.sessionDetails?.role_id
            const canAccess = userRoleDetails?.permission?.users?.edit ?? false

            if (!canAccess) {
                const isDifferentNotifications = differenceWith(
                    userRoleInfo.notifications,
                    body.notifications,
                    (userNotification, bodyNotification) => {
                        return userNotification.notification_type === bodyNotification.notification_type &&
                            userNotification.allow_push_notification === bodyNotification.allow_push_notification &&
                            userNotification.allow_sms_notification === bodyNotification.allow_sms_notification &&
                            userNotification.allow_email_notification === bodyNotification.allow_email_notification
                    }
                ).length > 0

                if (
                    userDetails?.is_active !== body.isActive ||
                    userRoleInfo.allow_price_change !== body.allowPriceChange ||
                    stringifyObjectId(userRoleInfo.role_id) !== body.roleId ||
                    isDifferentNotifications
                ) {
                    return res.handler.forbidden('access_denied');
                }
            }

            if (body.isActive && !userRoleInfo.is_active) {
                const defaultTenantSetting = await SystemPortalModal.getTenantDetailsById(body.tenantId);
                let tenantAdvanceLimit = defaultTenantSetting.advance_limit.find(data => data.key === TENANT_ADVANCE_LIMIT_KEYS.NUMBER_OF_USERS);
                const allowUsers = tenantAdvanceLimit.allowance
                const activeUsers = await TenantPortalModal.allowNewValidation(body.tenantId, TENANT_ALLOW_VALIDATION.USER)
                if (allowUsers <= activeUsers) {
                    // userRoleInfo.is_active = false;
                    return res.handler.conflict("user_limit_exceed");
                }
            }

            /**
             * If salesperson status changed to inactive,
             * then remove salesperson from work time config.
             */
            if (currentRoleInfo.name === PRIMITIVE_ROLES.SALES_PERSON) {
                if (
                    (currentRoleInfo.id !== roleInfo.id) ||
                    (userRoleInfo.is_active && !body.isActive)
                ) {
                    try {
                        const salespersonIds = [userRoleInfo._id]

                        await InternalServiceModal.removeSalespersonFromTracking(
                            req,
                            {
                                tenantId: body.tenantId,
                                salespersonIds,
                            }
                        )
                    }
                    catch (e) {
                        logger.error(e)
                    }
                }
            }

            userRoleInfo.is_active = body.isActive;
            userRoleInfo.notifications = body.notifications;

            // -- if current role is of Supervisor and now role has been changed, then update the supervisor which are assigned to sales person
            if ((currentRoleInfo.name === "Supervisor" && currentRoleInfo.id !== roleInfo.id)) {
                if ((userDetails._id.toString() === body.newSupervisorId)) {
                    return res.handler.notFound("validation_invalid_supervisor");
                }
                if (body.newSupervisorId) {
                    const newSupervisor = await TenantPortalModal.findSuperVisorWithUserId(body.newSupervisorId, body.tenantId, body.branchId);
                    if (!newSupervisor) {
                        return res.handler.notFound("validation_not_found_supervisor");
                    }
                    await TenantPortalModal.updateSupervisorOfSales(userRoleInfo.user_id, body.tenantId, body.newSupervisorId)
                }
            }


            if ((userRoleInfo.user_id.toString() !== userDetails._id.toString()) || (roleInfo.id !== currentRoleInfo.id)) {
                // mark user session as user role changed
                await TenantPortalModal.markSessionAsRoleChanged(userRoleInfo._id);
            }

            switch (body.portalType) {
                case VALUES.portals.TENANT_PORTAL: // when the request is fired from
                    // ---------- VALIDATORS ------------- //
                    break;

                case VALUES.portals.BRANCH_PORTAL:
                    // ---------- VALIDATORS ------------- //

                    break;
            }

            switch (roleInfo.portal_type) {
                case VALUES.portals.TENANT_PORTAL:
                    let tenantRoles = await RoleModal.getTenantPortalRoleByTenantId()
                    var userRole = await TenantPortalModal.findExistingTenantPortalProfile(body.tenantId, userDetails._id, tenantRoles);
                    if (userRole && userRole.id !== body.userRoleId) {
                        return res.handler.badRequest("validation_exists_assigned_role_branch");
                    }
                    if (roleInfo.name === PRIMITIVE_ROLES.TENANT_ADMIN) {
                        //Allow posting of voucher receipts option to admin profile. Only owner can give this permission
                        if ("allowPostingVoucherReceipts" in body) {
                            if (
                                userRoleInfo.hasOwnProperty('allow_posting_voucher_receipts') &&
                                body.allowPostingVoucherReceipts !== userRoleInfo.allow_posting_voucher_receipts
                            ) {
                                if (userRoleDetails?.name !== PRIMITIVE_ROLES.TENANT_OWNER) {
                                    return res.handler.validationError("cant_update_allow_posting_voucher_receipts")
                                }
                            }

                            if (
                                !userRoleInfo.hasOwnProperty('allow_posting_voucher_receipts') &&
                                body.allowPostingVoucherReceipts === true &&
                                userRoleDetails?.name !== PRIMITIVE_ROLES.TENANT_OWNER
                            ) {
                                userRoleInfo.allow_posting_voucher_receipts = false
                            }
                            else {
                                userRoleInfo.allow_posting_voucher_receipts = body.allowPostingVoucherReceipts
                            }
                        }

                        userRoleInfo.role_id = roleInfo._id;
                        userRoleInfo.supervisor_id = undefined;
                        userRoleInfo.branch_id = undefined;
                        userRoleInfo.updated_by = req.headers.userDetails._id;
                        userRoleInfo.allow_price_change = body.allowPriceChange;
                        userRoleInfo.allow_all_permission = true;
                    }
                    else if (roleInfo.name === PRIMITIVE_ROLES.TENANT_OWNER) {
                        // nothing should change for owner
                    }
                    else if (
                        [
                            PRIMITIVE_ROLES.WAREHOUSE_CLERK,
                            PRIMITIVE_ROLES.ACCOUNTANT,
                            PRIMITIVE_ROLES.CONTRIBUTOR,
                        ].includes(roleInfo.name)
                    ) {
                        userRoleInfo.role_id = roleInfo._id;
                        userRoleInfo.supervisor_id = undefined;
                        userRoleInfo.branch_id = undefined;
                        userRoleInfo.updated_by = req.headers.userDetails._id;
                        userRoleInfo.allow_price_change = body.allowPriceChange;
                        userRoleInfo.allow_all_permission = undefined;
                        userRoleInfo.allow_posting_voucher_receipts = undefined

                        if (
                            [
                                PRIMITIVE_ROLES.ACCOUNTANT,
                                PRIMITIVE_ROLES.CONTRIBUTOR,
                            ].includes(roleInfo.name)
                        ) {
                            //Don't sent any notification to Accountant & Contributor
                            userRoleInfo.notifications.forEach(notification => {
                                notification.allow_push_notification = false
                                notification.allow_sms_notification = false
                                notification.allow_email_notification = false
                            })
                        }
                    }
                    break;

                case VALUES.portals.BRANCH_PORTAL:
                    var [branchRoles, branchInfo] = await Promise.all([
                        RoleModal.getStandardBranchPortalRoles(),
                        TenantPortalModal.findBranchById(body.branchId)
                    ]);
                    if (!branchInfo || branchInfo.is_deleted || branchInfo.tenant_id !== body.tenantId) {
                        return res.handler.notFound("validation_not_found_branch");
                    }

                    var roleIndex = branchRoles.findIndex(r => r.id === roleInfo.id)
                    if (roleIndex === -1) {
                        return res.handler.notFound("validation_not_found_role")
                    }

                    var userRole = await TenantPortalModal.findExistingBranchPortalProfile(body.tenantId, branchInfo._id, userDetails._id, branchRoles.map(r => r._id));
                    if (userRole && userRole.id !== body.userRoleId) {
                        return res.handler.badRequest("validation_exists_assigned_role_branch");
                    }

                    if (roleInfo.name.toLowerCase() === "branch manager") {
                        userRoleInfo.role_id = roleInfo._id;
                        userRoleInfo.supervisor_id = undefined;
                        userRoleInfo.branch_id = body.branchId;
                        userRoleInfo.updated_by = req.headers.userDetails._id;
                        userRoleInfo.allow_price_change = body.allowPriceChange;
                        userRoleInfo.allow_all_permission = undefined;
                        userRoleInfo.allow_posting_voucher_receipts = undefined
                    }
                    break;
                case VALUES.portals.SALES_APP:
                    {
                        var [salesAppRoles, branchInfo] = await Promise.all([
                            RoleModal.getStandardSalesAppRoles(),
                            TenantPortalModal.findBranchById(body.branchId)
                        ]);
                        const roleNameLower = roleInfo.name.toLowerCase();
                        if (roleNameLower === "sales person" && (!branchInfo || branchInfo.is_deleted || branchInfo.tenant_id !== body.tenantId)) {
                            return res.handler.notFound("validation_not_found_branch");
                        }

                        var roleIndex = salesAppRoles.findIndex(r => r.id === roleInfo.id)
                        if (roleIndex === -1) {
                            return res.handler.notFound("validation_not_found_role")
                        }

                        // var userRole = await TenantPortalModal.findExistingSalesAppProfile(body.tenantId, roleNameLower === "sales person" ? branchInfo._id : null, userDetails._id, salesAppRoles.map(r => r._id))
                        const query = { _id: { $ne: body.userRoleId }, user_id: userDetails._id, role_id: { $in: salesAppRoles.map(r => r._id) }, tenant_id: body.tenantId, is_deleted: false };
                        var userRole = await TenantPortalModal.findUserProfileWithFilter(query)
                        // if (userRole && userRole.id !== body.userRoleId && !userRole.is_deleted) {
                        if (userRole) {
                            return res.handler.badRequest("validation_exists_assigned_role_branch");
                        }
                        // else if (userRole && userRole.id !== body.userRoleId && userRole.is_deleted) {
                        //     await userRole.deleteOne();
                        // }

                        if (roleNameLower === "sales person") {
                            if (body.supervisorId) {
                                const supervisorRoleInfo = await TenantPortalModal.findSuperVisorWithUserId(body.supervisorId, body.tenantId, body.branchId);
                                if (!supervisorRoleInfo || supervisorRoleInfo.is_deleted) {
                                    return res.handler.notFound("validation_not_found_supervisor")
                                }
                                if (!supervisorRoleInfo.user_id || supervisorRoleInfo.user_id.is_deleted) {
                                    return res.handler.notFound("validation_not_found_supervisor")
                                }
                            }
                            userRoleInfo.role_id = roleInfo._id;
                            userRoleInfo.supervisor_id = body.supervisorId;
                            userRoleInfo.branch_id = body.branchId;
                            userRoleInfo.updated_by = req.headers.userDetails._id;
                            userRoleInfo.allow_price_change = body.allowPriceChange;
                            userRoleInfo.allow_all_permission = undefined;
                            userRoleInfo.allow_posting_voucher_receipts = undefined
                        }
                        // else if (roleInfo.name.toLowerCase() === "supervisor") {
                        //     userRoleInfo.role_id = roleInfo._id;
                        //     userRoleInfo.supervisor_id = undefined;
                        //     userRoleInfo.branch_id = null;
                        //     userRoleInfo.updated_by = req.headers.userDetails._id;
                        //     userRoleInfo.allow_price_change = body.allowPriceChange;
                        //     userRoleInfo.allow_all_permission = true;
                        // }
                        break;

                    }

                case VALUES.portals.SUPERVISOR_APP:
                    {
                        const SupervisorRole = await RoleModal.getRoleByFilter({ name: PRIMITIVE_ROLES.SUPERVISOR }, { _id: 1 });
                        var userRole = await TenantPortalModal.findExistingSalesAppProfile(body.tenantId, null, userDetails._id, [SupervisorRole._id]);

                        if (userRole && userRole.id !== body.userRoleId && !userRole.is_deleted) {
                            return res.handler.badRequest("validation_exists_assigned_role_branch");
                        } else if (userRole && userRole.id !== body.userRoleId && userRole.is_deleted) {
                            await userRole.deleteOne();
                        }

                        userRoleInfo.role_id = roleInfo._id;
                        userRoleInfo.supervisor_id = undefined;
                        userRoleInfo.branch_id = null;
                        userRoleInfo.updated_by = req.headers.userDetails._id;
                        userRoleInfo.allow_price_change = body.allowPriceChange;
                        userRoleInfo.allow_all_permission = true;
                        userRoleInfo.allow_posting_voucher_receipts = undefined
                        break;
                    }
            }

            if (currentRoleInfo.name === "Sales Person" && ((currentRoleInfo.id !== roleInfo.id))) {
                if (body.newSalesPersonId === body.userRoleId) {
                    return res.handler.conflict("not_valid_new_sales_person_id");
                }
                const newSalesPerson = await TenantPortalModal.findSalesPersonWithUserRoleId(body.newSalesPersonId, body.tenantId);
                if (!newSalesPerson) {
                    return res.handler.notFound("salesperson_not_found");
                }

                await TenantPortalModal.updateCustomerSalesPerson(userRoleInfo._id, body.tenantId, newSalesPerson._id, newSalesPerson.branch_id);
            }

            // update after checking Supervisor role changes
            userRoleInfo.user_id = userDetails._id;
            userRoleInfo.collection_name = "users";
            await userRoleInfo.save();
            if (!newUser) {
                await UserAuthModal.updateCognitoUserNameAndEmail(updateUserDetails, req.headers);
                await userDetails.save();
            } else {
                await UserAuthModal.sendWelcomePortalUser(userDetails);
            }

            return res.handler.success("updated_edited");
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async deleteTenantBranchPortalUser(req, res) {
        try {
            const {
                userRoleId,
                newSupervisorId,
                newSalesPersonId,
            } = req.query;

            const userRole = await TenantPortalModal.getTenantBranchPortalUserById(userRoleId);

            if (!userRole || userRole.is_deleted) {
                return res.handler.notFound("validation_not_found_user");
            }

            const {
                _id: user_role_id,
                user_id,
                tenant_id,
                role_id: {
                    name: role_name,
                } = {},
            } = userRole

            if (role_name === PRIMITIVE_ROLES.SUPERVISOR) {
                await TenantPortalModal.updateSupervisorOfSales(
                    user_id,
                    tenant_id,
                    newSupervisorId,
                )
            }
            const isSalespersonRole = role_name === PRIMITIVE_ROLES.SALES_PERSON

            //check customer count for the salesperson, if it does not, no need to check for newSalesPersonId
            if (isSalespersonRole) {
                const customerExists = await TenantPortalModal.findUserProfileWithFilter(
                    {
                        sales_person_id: user_role_id,
                        is_deleted: false
                    },
                    { _id: 1 },
                    { lean: true }
                );

                if (customerExists) {
                    const newSalesPerson = await TenantPortalModal.findSalesPersonWithUserRoleId(
                        newSalesPersonId,
                        tenant_id,
                    )

                    if (!newSalesPerson) {
                        return res.handler.notFound("salesperson_not_found");
                    }

                    const result = await TenantPortalModal.updateCustomerSalesPerson(
                        user_role_id,
                        tenant_id,
                        newSalesPerson._id,
                    );
                }
            }

            // Delete tenant/branch portal user
            await TenantPortalModal.deleteTenantBranchPortalUser(user_role_id)

            if (isSalespersonRole) {
                // Remove salesperson from work time config.
                try {
                    const salespersonIds = [user_role_id]

                    await InternalServiceModal.removeSalespersonFromTracking(
                        req,
                        {
                            tenantId: tenant_id,
                            salespersonIds,
                        }
                    )
                }
                catch (e) {
                    logger.error(e)
                }
            }
            return res.handler.success("deleted_user");
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async checkExistingExternalId(req, res) {
        try {
            const { tenantId, accountNumber } = req.query

            const existingExternalId = await TenantPortalModal.findUserProfileWithFilter(
                {
                    "unique_external_id": `${tenantId}_${accountNumber}`,
                    "is_deleted": false,
                },
                "_id external_id unique_external_id",
                { lean: true }
            )
            return res.handler.success(null, { existingExternalId })
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async addCustomer(req, res) {
        try {
            const body = req.body;
            body.tenantId = Number(body.tenantId);
            body.gps_coordinates = { longitude: body.longitude, latitude: body.latitude };
            let userDetails;
            let newUser = false;

            const [existingCustomer, tenantInfo, salesPerson] = await Promise.all([
                // Get existing customer with same mobile number
                TenantPortalModal.findTenantCustomer(
                    {
                        unique_mobile_number: body.countryCode + body.mobileNumber,
                        is_deleted: false,
                    },
                    undefined,
                    toLeanOption,
                ),

                // Get tenant info
                SystemPortalModal.getTenantById(body.tenantId),

                // Get salesperson info
                TenantPortalModal.findSalesPersonWithUserRoleId(
                    body.salesPersonId,
                    body.tenantId,
                    "is_deleted",
                    toLeanOption
                )
            ]);

            if (!tenantInfo || tenantInfo.is_deleted) {
                return res.handler.notFound("tenant_not_found");
            }
            if (!salesPerson || salesPerson.is_deleted) {
                return res.handler.notFound("salesperson_not_found");
            }

            userDetails = existingCustomer
            if (!userDetails) {
                newUser = true;
            }
            // let customerRoles = await RoleModal.getCustomerRole();
            // customerRoles = customerRoles.map(tr => {
            //     return tr._id
            // })
            const customerRole = await RoleModal.getRoleByFilter(
                {
                    name: "Customer",
                    portal_type: VALUES.portals.CUSTOMER_APP,
                    is_active: true,
                    is_deleted: false,
                },
                { _id: 1 },
                toLeanOption,
            );

            let userRole;
            let message;
            //NOTE: can create multiple customer with same primary contact number with same salesperson
            /* if (userDetails) {
                // userRole = await TenantPortalModal.findExistingTenantPortalProfile(body.tenantId, userDetails._id, customerRoles);
                userRole = await TenantPortalModal.findUserProfileWithFilter({
                    tenant_id: tenantInfo._id,
                    user_id: userDetails._id,
                    role_id: customerRole._id,
                    sales_person_id: body.salesPersonId
                });
            } */

            if (FALSELY_VALUES.includes(body.accountNumber)) {
                body.accountNumber = ""
            }

            if (userRole) {
                if (userRole.is_deleted) {
                    userRole.is_deleted = false;
                    userRole.tenant_id = body.tenantId;
                    // userRole.branch_id= body.branchId;
                    userRole.role_id = customerRole._id;
                    userRole.sales_person_id = body.salesPersonId;
                    userRole.customer_app_access = body.customerAppAccess || false;
                    userRole.customer_catalog_mode = body.customerCatalogMode || false;
                    userRole.is_active = body.isActive;
                    userRole.preferred_language = body.preferredLanguage;
                    userRole.external_id = body.accountNumber;
                    userRole.customer_name = body.customerName;
                    userRole.customer_legal_name = body.legalName;
                    userRole.notifications = body.notifications;
                    userRole.shipping_address = body.shippingAddress;
                    // userRole.shipping_country_id = body.shippingCountryId;
                    // userRole.shipping_country_id = body.shippingCountryId;
                    userRole.shipping_country_id = tenantInfo.country;
                    userRole.shipping_city_id = body.shippingCityId;
                    userRole.shipping_region_id = body.shippingRegionId;
                    userRole.shipping_country_code = body.shippingCountryCode;
                    userRole.shipping_mobile_number = body.shippingMobileNumber;
                    userRole.gps_coordinates = body.gps_coordinates;
                    userRole.price_list_id = body.priceListId;
                    userRole.device_access = body.deviceAccess
                    userRole.is_verified = body.isVerified || false;
                    userRole.collection_name = "tenant_customers"

                    userRole.user_id = userDetails._id;
                    userRole.customer_email = body.email;
                    userRole.customer_first_name = body.firstName;
                    userRole.customer_last_name = body.lastName;
                    await userRole.save();
                    message = "restored_user"

                } else {
                    return res.handler.conflict("VALIDATION.EXISTS.CUSTOMER")
                }
            }
            else {
                var customerCount = tenantInfo.customer_counter += 1
                if (newUser) {
                    //     req.body.password = "66G1!d8LviS02x@5i$7"
                    //     req.body.email = req.body.email || ('' + body.tenantId + customerCount + "@hawak.io");
                    //     const user = await UserAuthModal.signup(req.body);
                    //     const cognitoData = await UserAuthModal.creatUserInUserPool(req.body);
                    //     user.cognito_username = cognitoData.userSub;
                    //     const otp = generateOtp();
                    //     user.forgot_password_otp = otp;
                    //     user.otp_verification_time = new Date();
                    //     user.otp_verification_time_limit = VALUES.PASSWORD_REST_OR_CREATE_DURATION;
                    //     // save user in DB
                    //     await user.save();
                    //     userDetails = user;
                    //     await UserAuthModal.sendEMailVerificationLink(req.body.email, otp, user._id.toString() ,"Reset Password");
                    //     await Promise.all([UserAuthModal.adminConfirmSignUp(req.body), UserAuthModal.verifyUserDetails(req.body)]);

                    //TODO: need to create unique index with property (country_code + mobile number) for handling concurrency issue, so the system do not create multiple tenant_customer  with same mobile number
                    userDetails = await TenantPortalModal.addTenantCustomer();
                    userDetails.country_code = body.countryCode;
                    userDetails.mobile_number = body.mobileNumber;
                    userDetails.unique_mobile_number = userDetails.country_code + userDetails.mobile_number;
                    userDetails.created_by = req.headers.userDetails._id;
                    userDetails.updated_by = req.headers.userDetails._id;

                    await userDetails.save();
                }

                body.tenant_id = body.tenantId;
                body.branch_id = body.branchId;
                body.user_id = userDetails._id;
                body.customer_id = '' + body.tenantId + customerCount;
                body.role_id = customerRole._id;
                body.sales_person_id = body.salesPersonId;
                body.customer_app_access = body.customerAppAccess || false;
                body.customer_catalog_mode = body.customerCatalogMode || false;
                body.is_active = body.isActive;
                body.preferred_language = body.preferredLanguage;
                body.external_id = body.accountNumber;

                if (body.external_id) {
                    body.unique_external_id = `${body.tenant_id}_${body.external_id}`
                }
                body.customer_name = body.customerName;
                body.customer_legal_name = body.legalName;
                body.notifications = body.notifications;
                body.shipping_address = body.shippingAddress;
                body.shipping_country_id = body.shippingCountryId;
                body.shipping_city_id = body.shippingCityId;
                body.shipping_region_id = body.shippingRegionId;
                body.shipping_country_code = body.shippingCountryCode;
                body.shipping_mobile_number = body.shippingMobileNumber;
                body.gps_coordinates = body.gps_coordinates;
                body.price_list_id = body.priceListId;
                body.device_access = body.deviceAccess;
                body.is_verified = body.isVerified || false;
                // body.catalog_mode = body.catalogMode || false;
                body.is_deleted = false;
                body.user_id = userDetails._id;
                body.customer_email = body.email;
                body.customer_first_name = body.firstName;
                body.customer_last_name = body.lastName;
                body.created_by = req.headers.userDetails._id;
                body.updated_by = req.headers.userDetails._id;
                body.collection_name = "tenant_customers";

                // userRole = await TenantPortalModal.addCustomer(body, req.headers);
                userRole = await TenantPortalModal.addCustomerProfile(body);
                message = "ADDED.CUSTOMER"
            }
            if (userRole.customer_app_access) {
                userRole.customer_app_request = false;
            } else {
                userRole.customer_app_request = body.customerAppRequest ? body.customerAppRequest : false;
            }

            await Promise.all([
                // Save customer into DB
                userRole.save(),

                // Update tenant info
                tenantInfo.save(),

                // Upsert customer's user role setting
                TenantPortalModal.updateUserRoleSetting(
                    { _id: `${body.tenantId}_${userRole._id}` },
                    {
                        $set: {
                            _id: `${body.tenantId}_${userRole._id}`,
                            tenant_id: body.tenantId,
                            user_role_id: userRole._id,
                            default_master_price_id: body.priceListId,
                            out_of_stock: {
                                visible: false,
                                searchable: false,
                            },
                            price_change: false,
                            preferred_language: body.preferredLanguage,
                        }
                    },
                    { upsert: true, new: true }
                )
            ]);

            // Populate the userRole with related data
            let populatedUserRole = {};
            if(req.headers.deviceaccesstype === VALUES.deviceAccessType.TABLET) {
                populatedUserRole = await TenantPortalModal.getTabletCustomerWithRoleById(userRole._id);
            }

            const returnResponse = {
                _id: userRole._id,
                customer_id: userRole.customer_id,
                customer_details : populatedUserRole
            }
            return res.handler.success(message, returnResponse);
        }
        catch (error) {
            if (error.code === STATUS_CODES.MONGODB_DUPLICATE_KEY_CODE) {
                if ("unique_external_id" in error.keyValue) {
                    return res.handler.conflict("external_id_already_exists")
                }
                if ("unique_mobile_number" in error.keyValue) {
                    return res.handler.conflict("mobile_number_already_exists")
                }
                if ("customer_id" in error.keyValue) {
                    return res.handler.conflict("customer_id_already_exists")
                }
            }

            return res.handler.serverError(error);
        }
    }

    async getCustomer(req, res) {
        try {
            const {
                customerUserRoleIds,
                userRoleId,
                tenantId,
                projections = []
            } = req.query;

            if (userRoleId) {
                const userRole = await TenantPortalModal.getCustomerPortalUserById(
                    userRoleId,
                    projections.join(" "),
                    toLeanOption
                );

                if (!userRole || userRole.is_deleted) {
                    return res.handler.notFound("customer_not_found");
                }

                return res.handler.success(null, userRole);
            }
            else if (Array.isArray(customerUserRoleIds)) {
                if (!tenantId) {
                    return res.handler.badRequest("invalid_tenant_id")
                }

                const customerRole = await RoleModal.getRoleByFilter(
                    { name: PRIMITIVE_ROLES.CUSTOMER },
                    undefined,
                    toLeanOption,
                );

                let project = {
                    tenant_id: 1,
                    customer_name: 1,
                    customer_legal_name: 1,
                    external_id: 1,
                    customer_first_name: 1,
                    customer_last_name: 1,
                    is_payment_enabled: 1,
                    created_at: 1,
                    price_list_id: 1,
                }

                if (projections.length) {
                    project = projections.join(" ")
                }

                const customers = await TenantPortalModal.findUserProfilesWithFilter(
                    {
                        _id: { $in: customerUserRoleIds },
                        role_id: customerRole._id,
                        tenant_id: tenantId
                    },
                    project,
                    toLeanOption,
                );

                const customerObject = {};

                customers.forEach((cus) => {
                    customerObject[cus._id] = cus
                })

                return res.handler.success(null, customerObject);
            }
            else {
                return res.handler.badRequest("invalid_request_params");
            }
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async editCustomer(req, res) {
        try {
            const body = req.body;
            body.tenantId = Number(body.tenantId);
            body.gps_coordinates = { longitude: body.longitude, latitude: body.latitude };
            let userDetails;

            const [existingUser, userRoleInfo, customerRole, tenantInfo, salesPerson] = await Promise.all([
                // Get existing customer with same mobile number
                TenantPortalModal.findTenantCustomer(
                    {
                        unique_mobile_number: body.countryCode + body.mobileNumber,
                        is_deleted: false,
                    },
                    undefined,
                    toLeanOption,
                ),

                // Get customer info
                TenantPortalModal.getOnlyUserRoleById(req.body.userRoleId),

                // Get customer role info
                RoleModal.getRoleByFilter(
                    {
                        name: "Customer",
                        portal_type: VALUES.portals.CUSTOMER_APP,
                        is_active: true,
                        is_deleted: false,
                    },
                    { _id: 1 }
                ),

                // Get tenant info
                SystemPortalModal.getTenantById(body.tenantId, "country", toLeanOption),

                // Get salesperson info
                TenantPortalModal.findSalesPersonWithUserRoleId(
                    body.salesPersonId,
                    body.tenantId,
                    "is_deleted",
                    toLeanOption,
                )
            ]);

            if (userRoleInfo.role_id.toString() !== customerRole._id.toString()) {
                await sendFailureEmail(req, "Invalid user role id")
                return res.handler.badRequest("invalid_role_id");
            }

            if (!userRoleInfo || userRoleInfo.is_deleted || !userRoleInfo.customer_id) {
                await sendFailureEmail(req, "User role not found")
                return res.handler.notFound("validation_not_found_user_role");
            }

            if (!salesPerson || salesPerson.is_deleted) {
                await sendFailureEmail(req, "Salesperson not found")
                return res.handler.notFound("salesperson_not_found");
            }

            if (userRoleInfo.tenant_id !== body.tenantId) {
                await sendFailureEmail(req, "Tenant id is mismatched with user role")
                return res.handler.badRequest();
            }

            if (FALSELY_VALUES.includes(body.accountNumber)) {
                body.accountNumber = ""
            }

            if (userRoleInfo.is_payment_enabled) {
                if (body.legalName !== userRoleInfo.customer_legal_name) {
                    await sendFailureEmail(req, "Can't update legal name as payment is enabled")
                    return res.handler.validationError("cant_update_legal_name_as_payment_enabled")
                }

                if (body.accountNumber !== userRoleInfo.external_id) {
                    await sendFailureEmail(req, "Can't update external id as payment is enabled")
                    return res.handler.validationError("cant_update_external_id_as_payment_enabled")
                }
            }

            if (body.isActive && !userRoleInfo.is_active) {  // Dec 30 users and customer limit validation
                const defaultTenantSetting = await SystemPortalModal.getTenantWithFilter(
                    { _id: body.tenantId },
                    { advance_limit: 1 },
                    toLeanOption,
                )

                if (!defaultTenantSetting) {
                    await sendFailureEmail(req, "Default tenant setting not found for checking 'NUMBER_OF_CUSTOMERS' limit")
                    return res.handler.notFound("default_tenant_setting_not_found");
                }

                let tenantAdvanceLimit = defaultTenantSetting.advance_limit.find(data => data.key === 'NUMBER_OF_CUSTOMERS');
                const allowCustomers = tenantAdvanceLimit.allowance
                const activeCustomers = await TenantPortalModal.allowNewValidation(body.tenantId, TENANT_ALLOW_VALIDATION.CUSTOMER)
                if (allowCustomers <= activeCustomers) {
                    // userRoleInfo.is_active = false;
                    await sendFailureEmail(req, "Customer limit exceeded")
                    return res.handler.conflict("customer_limit_exceed");
                }
            } // Dec 30 users and customer limit validation

            userDetails = existingUser;
            if (!userDetails) {
                userDetails = await TenantPortalModal.addTenantCustomer();
                userDetails.country_code = body.countryCode;
                userDetails.mobile_number = body.mobileNumber;
                userDetails.created_by = req.headers.userDetails._id;
                userDetails.updated_by = req.headers.userDetails._id;
                userDetails.unique_mobile_number = userDetails.country_code + userDetails.mobile_number;
                await userDetails.save();
            }

            //NOTE: can create multiple customer with same primary contact number with same salesperson
            // if (userDetails.id !== userRoleInfo.user_id.toString()) {
            //     let existingUserProfile = await TenantPortalModal.findUserProfileWithFilter({ tenant_id: tenantInfo._id, user_id: userDetails._id, role_id: customerRole._id, _id: { $ne: userRoleInfo._id }, sales_person_id: body.salesPersonId });
            //     if (existingUserProfile && existingUserProfile.is_deleted) {
            //         await existingUserProfile.deleteOne();
            //     } else if (existingUserProfile && !existingUserProfile.is_deleted) {
            //         return res.handler.conflict("validation_exists_customer")
            //     }
            // }

            userRoleInfo.tenant_id = body.tenantId;
            userRoleInfo.branch_id = body.branchId;
            userRoleInfo.user_id = userDetails._id;
            userRoleInfo.role_id = customerRole._id;
            userRoleInfo.sales_person_id = body.salesPersonId;
            userRoleInfo.customer_app_access = body.customerAppAccess !== undefined ? body.customerAppAccess : userRoleInfo.customer_app_access;
            if (body.customerCatalogMode !== undefined) {
                userRoleInfo.customer_catalog_mode = body.customerCatalogMode
            }
            userRoleInfo.is_active = body.isActive;
            // if (userRoleInfo.is_active) {  // Dec 30 users and customer limit validation
            //     const defaultTenantSetting = await SystemPortalModal.getTenantDetailsById(body.tenantId)
            //     let tenantAdvanceLimit = defaultTenantSetting.advance_limit.find(data => data.key === 'NUMBER_OF_CUSTOMERS');
            //     const allowCustomers = tenantAdvanceLimit.allowance
            //     const activeCustomers = await TenantPortalModal.allowNewValidation(body.tenantId, TENANT_ALLOW_VALIDATION.CUSTOMER)
            //     if (allowCustomers <= activeCustomers) {
            //         // userRoleInfo.is_active = false;
            //         return res.handler.conflict("customer_limit_exceed");
            //     }
            // } // Dec 30 users and customer limit validation

            if (
                body.preferredLanguage !== userRoleInfo.preferred_language ||
                body.priceListId !== userRoleInfo.price_list_id.toString()
            ) {
                const userSetting = await TenantPortalModal.findUserRoleSetting(
                    { _id: `${body.tenantId}_${req.body.userRoleId}` },
                    excludeProjections,
                    toLeanOption,
                )

                const newSettings = {
                    default_master_price_id: body.priceListId,
                    preferred_language: body.preferredLanguage,
                }

                if (!userSetting) {
                    newSettings._id = `${body.tenantId}_${req.body.userRoleId}`
                    newSettings.tenant_id = body.tenantId
                    newSettings.user_role_id = req.body.userRoleId
                    newSettings.out_of_stock = {
                        visible: false,
                        searchable: false,
                    }
                    newSettings.price_change = false
                }

                await TenantPortalModal.updateUserRoleSetting(
                    { _id: `${body.tenantId}_${req.body.userRoleId}` },
                    { $set: newSettings },
                    { upsert: true, new: true }
                );
            }

            userRoleInfo.preferred_language = body.preferredLanguage;
            userRoleInfo.external_id = body.accountNumber;

            if (body.accountNumber) {
                userRoleInfo.unique_external_id = `${body.tenantId}_${body.accountNumber}`
            }
            else {
                userRoleInfo.unique_external_id = null
            }

            userRoleInfo.customer_name = body.customerName;
            userRoleInfo.customer_legal_name = body.legalName;
            userRoleInfo.notifications = body.notifications;
            userRoleInfo.shipping_address = body.shippingAddress;
            // userRoleInfo.shipping_country_id = body.shippingCountryId;
            userRoleInfo.shipping_country_id = tenantInfo.country;
            userRoleInfo.shipping_city_id = body.shippingCityId;
            userRoleInfo.shipping_region_id = body.shippingRegionId;
            userRoleInfo.shipping_country_code = body.shippingCountryCode;
            userRoleInfo.shipping_mobile_number = body.shippingMobileNumber;
            userRoleInfo.gps_coordinates = body.gps_coordinates;
            userRoleInfo.price_list_id = body.priceListId;

            userRoleInfo.user_id = userDetails._id;
            userRoleInfo.customer_email = body.email;
            userRoleInfo.customer_first_name = body.firstName;
            userRoleInfo.customer_last_name = body.lastName;
            userRoleInfo.created_by = req.headers.userDetails._id;
            userRoleInfo.updated_by = req.headers.userDetails._id;
            userRoleInfo.collection_name = "tenant_customers";

            if (userRoleInfo.customer_app_access) {
                userRoleInfo.customer_app_request = false;
            } else {
                userRoleInfo.customer_app_request = body.customerAppRequest === undefined ? userRoleInfo.customer_app_request : body.customerAppRequest;
            }

            await userRoleInfo.save();
            
            // Populate the userRoleInfo with related data
            let populatedUserRole = userRoleInfo;
            if(req.headers.deviceaccesstype === VALUES.deviceAccessType.TABLET) {
                populatedUserRole = await TenantPortalModal.getTabletCustomerWithRoleById(userRoleInfo._id);
            }
            
            return res.handler.success("updated_edited" , {
                customer_id: userRoleInfo.customer_id,
                customer_details : populatedUserRole
            });
        }
        catch (error) {
            if (error.code === STATUS_CODES.MONGODB_DUPLICATE_KEY_CODE) {
                if ("unique_external_id" in error.keyValue) {
                    await sendFailureEmail(req, "External id already exists")
                    return res.handler.conflict("external_id_already_exists")
                }
                if ("unique_mobile_number" in error.keyValue) {
                    await sendFailureEmail(req, "Mobile number already exists")
                    return res.handler.conflict("mobile_number_already_exists")
                }
            }
            await sendFailureEmail(req, error)
            return res.handler.serverError(error);
        }
    }

    async deleteCustomer(req, res) {
        try {
            const body = req.query;
            const userRole = await TenantPortalModal.getCustomerWithRoleById(body.userRoleId);
            if (!userRole || userRole.is_deleted) {
                return res.handler.notFound("validation_not_found_customer");
            }

            if (userRole.role_id.name !== "Customer") {
                return res.handler.badRequest("not_supported_role");
            }

            await TenantPortalModal.deleteCustomer(userRole._id)
            return res.handler.success("deleted_customer");
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async updateCustomerAppAccess(req, res) {
        try {
            const { customerUserRoleIds, accessInfo } = req.body;

            const filter = { _id: { $in: customerUserRoleIds } };
            const updateObject = { updated_by: req.headers.userDetails?._id };
            if (accessInfo.hasOwnProperty("appAccess")) {
                updateObject['customer_app_access'] = accessInfo.appAccess;
                updateObject['customer_app_request'] = false;
            }

            if (accessInfo.hasOwnProperty("catalogMode")) {
                updateObject['customer_catalog_mode'] = accessInfo.catalogMode;
            }

            await TenantPortalModal.updateUserProfilesWithFilter(filter, updateObject);

            return res.handler.success("updated_edited")

        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async addCustomerDeviceAccess(req, res) {
        try {
            const body = req.body;
            const userRole = await TenantPortalModal.getCustomerPortalUserById(body.userRoleId);
            if (userRole) {
                let findDeviceIndex = userRole.device_access.findIndex((value) => (value.device_id === body.deviceId))
                if (findDeviceIndex != -1) {
                    return res.handler.conflict("validation_exists_customer_device_id")
                }
                await TenantPortalModal.addCustomerDeviceAccess(body);
                return res.handler.success(null);
            }
            return res.handler.conflict("validation_not_found_customer")
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async deleteCustomerDeviceAccess(req, res) {
        try {
            const body = req.body;
            const userRole = await TenantPortalModal.getCustomerPortalUserById(body.userRoleId);

            if (userRole) {
                await TenantPortalModal.deleteCustomerDeviceAccess(body)
                return res.handler.success(null);
            }
            return res.handler.conflict("validation_not_found_customer")
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async tenantNonDeletedBranches(req, res) {
        try {
            let branches
            switch (req.query.type) {
                case BRANCH_LIST_TYPE.WAREHOUSE:
                    branches = await SystemPortalModal.getBranchesWithWareHouse(req.query.tenantId)
                    break;

                default:
                    branches = await SystemPortalModal.getTenantBranchesByTenantId(req.query.tenantId);
                    break;
            }
            return res.handler.success(null, branches)
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getPriceList(req, res) {
        try {
            const result = await TenantPortalModal.getPriceList(req.query.tenantId);
            return res.handler.success(null, result)
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getCustomers(req, res) {
        try {
            let customers = await TenantPortalModal.getCustomers(req.query, req.headers);
            if (req.query.withOrderStats) {
                const data = customers.list.map((item) => item._id)
                const orderStatus = await httpService(req).post("tenant/orderStatus", data)
                const objectOfOrderStatus = {}
                orderStatus.data.data.forEach((order) => objectOfOrderStatus[order._id] = order)
                customers.list = customers.list.map((customer) => ({ ...customer, ...objectOfOrderStatus[customer._id] }))
            } else if (req.query.onlyCustomerCount) {
                customers = customers[0] ? customers[0] : { customer_count: 0 }; //NOTE: THIS CHECK MIGHT NOT ABLE TO HANDLE CONCURRENCY, AND CAN LEAD TO SAVING WRONG SALESPERSON INFO IN ASSIGNED CUSTOMERS
            }
            return res.handler.success(null, customers)
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async updateCustomersAccesses(req, res) {
        try {
            const result = await TenantPortalModal.updateCustomerStatus(req.body, req.headers);
            return res.handler.success("updated_edited", result)
        }
        catch (error) {
            await sendFailureEmail(req, error)
            return res.handler.serverError(error);
        }
    }

    async updateCustomers(req, res) {
        try {
            const {
                ids,
                updateFields,
                tenantId
            } = req.body

            const validUpdateFields = [
                "customer_name",
                "customer_legal_name",
                "customer_first_name",
                "customer_last_name",
                "customer_email",
                "external_id",

                "preferred_language",
                "is_verified",

                "shipping_address",
                "shipping_country_id",
                "shipping_city_id",
                "shipping_region_id",
                "shipping_country_code",
                "shipping_mobile_number",
                "gps_coordinates",
            ]
            const toBeUpdateFields = []

            for (const field in updateFields) {
                toBeUpdateFields.push(field)
            }
            const invalidUpdateFields = difference(toBeUpdateFields, validUpdateFields)

            if (invalidUpdateFields.length > 0) {
                await sendFailureEmail(req, { invalidUpdateFields, validUpdateFields })
                return res.handler.badRequest(null, { invalidUpdateFields, validUpdateFields })
            }

            const filter = {
                "_id": {
                    $in: ids
                },
                "collection_name": "tenant_customers",
            }

            if (updateFields.external_id) {
                if (ids.length > 1) {
                    await sendFailureEmail(req, "Can't bulk update external id")
                    return res.handler.validationError("cant_bulk_update_external_id")
                }

                const existingCustomer = await TenantPortalModal.findUserProfileWithFilter(
                    {
                        "unique_external_id": `${tenantId}_${updateFields.external_id}`,
                        "is_deleted": false,
                    },
                    "_id customer_name",
                    { lean: true },
                )

                if (existingCustomer && existingCustomer._id.toString() !== ids[0]) {
                    const error = {
                        message: "External id already exist",
                        forCustomer: existingCustomer.customer_name
                    }

                    await sendFailureEmail(req, error)
                    return res.handler.validationError("external_id_already_exists", error)
                }

                updateFields.unique_external_id = `${tenantId}_${updateFields.external_id}`
            }

            if (updateFields.customer_legal_name || updateFields.external_id) {
                const customers = await TenantPortalModal.findUserProfilesWithFilter(
                    {
                        is_payment_enabled: true,
                        ...filter,
                    },
                    {
                        is_payment_enabled: 1,
                        customer_name: 1
                    },
                    {
                        lean: true
                    }
                )

                if (customers.length > 0) {
                    const error = {
                        message: "Can't update legal name or external id as payment is enabled for below customers",
                        customers: customers.map(customer => customer.customer_name)
                    }

                    await sendFailureEmail(req, error)
                    return res.handler.validationError("cant_update_legal_name_or_external_id_as_payment_enabled", error)
                }
            }

            await TenantPortalModal.updateBulkCustomers(filter, updateFields, req.headers)
            if(req.headers.deviceaccesstype === VALUES.deviceAccessType.TABLET) {
                const customer = await TenantPortalModal.getTabletCustomerWithRoleById(ids[0])
                return res.handler.success("updated_edited", {
                    customer_id: customer._id,
                    customer_details: customer
                })
            }

            return res.handler.success("updated_edited")
        }
        catch (error) {
            await sendFailureEmail(req, error)
            return res.handler.serverError(error)
        }
    }

    async getConfigurations(req, res) {
        try {
            const body = req.query;
            body.tenantId = Number(body.tenantId);

            switch (body.type) {
                case SETTINGS.CONFIGURATIONS.TYPE.SHIPPING_LABEL:
                    const shippingLabel = await TenantPortalModal.shippingLabelExist(body.tenantId);
                    if (shippingLabel.shipping_label_logo) {
                        shippingLabel.shipping_label_logo = process.env.AWS_PUBLIC_BUCKET_BASE_URL + "shipping-label" + `/${body.tenantId}/` + shippingLabel.shipping_label_logo;
                    }
                    return res.handler.success(null, shippingLabel)

                case SETTINGS.CONFIGURATIONS.TYPE.APP_SETTING:
                    const appSetting = await TenantPortalModal.tenantAppSettingExist(body.tenantId);

                    const tenantOrderPrefix = await TenantPortalModal
                        .findTenantOrderPrefix(
                            {
                                "tenant_id": body.tenantId,
                                "is_current": true,
                            },
                            {
                                "_id": 0,
                                "prefix": 1,
                                "counter": 1,
                            }
                        )

                    const responseData = {
                        ...(appSetting?.toObject() || {}),
                        ...(tenantOrderPrefix?.toObject() || {}),
                    }
                    return res.handler.success(null, responseData)
            }

            const tenantInfo = await TenantPortalModal.getTenantInfo(body.tenantId);
            return res.handler.success(null, tenantInfo)

        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async updateShippingLabel(req, res) {
        try {
            const {
                tenantId,
                tenantLegalName,
                branches,
                shippingLabelLogo,
                isActive,
            } = req.body

            const shippingLabel = await TenantPortalModal.shippingLabelExist(tenantId)

            if (!shippingLabel || !shippingLabel._id) {
                return res.handler.notFound()
            }

            if (shippingLabelLogo !== shippingLabel.shipping_label_logo) {
                /** Delete shipping logo from the s3 only if,
                 *  previous & current shipping logo name is different.
                */
                const publicS3 = new FileUpload(BUCKET_TYPE.PUBLIC)

                await publicS3.deleteFile(FILE_PATH.SHIPPING_LABEL + `/${tenantId}`, shippingLabel.shipping_label_logo)
            }

            branches.forEach(branch => {
                const {
                    branchId,
                    streetAddress,
                    mobileNumber,
                    mobileNumberCountryCode,
                    phoneNumber,
                    phoneNumberCountryCode,
                } = branch

                branch.branch_id = branchId
                branch.street_address = streetAddress
                branch.mobile_number = mobileNumber
                branch.mobile_number_country_code = mobileNumberCountryCode

                if (phoneNumber) {
                    branch.phone_number = phoneNumber
                }

                if (phoneNumberCountryCode) {
                    branch.phone_number_country_code = phoneNumberCountryCode
                }
            })

            shippingLabel.tenant_id = tenantId
            shippingLabel.tenant_legal_name = tenantLegalName
            shippingLabel.branches = branches
            shippingLabel.shipping_label_logo = shippingLabelLogo
            shippingLabel.is_active = isActive
            shippingLabel.updated_by = req.headers.userDetails._id
            await shippingLabel.save()

            return res.handler.success("updated_shipping_label")
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }

    async updateAppSetting(req, res) {
        try {
            const body = req.body;
            body.tenantId = Number(body.tenantId);

            const tenantExist = await TenantPortalModal.tenantAppSettingExist(body.tenantId)

            if (tenantExist) {

                tenantExist.tenant_id = body.tenantId;
                tenantExist.quantity_label = body.quantityLabel;
                tenantExist.consider_new_item = body.considerNewItem;
                tenantExist.price_change = body.priceChange;
                tenantExist.hide_out_of_stock_product = body.hideOutOfStockProduct;
                tenantExist.reduce_inventory = body.reduceInventory;
                tenantExist.customer_app_access = body.customerAppAccess;
                tenantExist.catalog_mode = body.catalogMode;
                tenantExist.customer_auto_catalog_mode = body.customerAutoCatalogMode;
                tenantExist.preferred_language = body.preferredLanguage;
                tenantExist.decimal_points = body.decimalPoint || 0;
                // tenantExist.payment_voucher_whatsapp_notification = body.payment_voucher_whatsapp_notification;
                tenantExist.updated_by = req.headers.userDetails._id;

                await tenantExist.save();
                await TenantPortalModal.updateTenantOrderPrefix(body, req.headers);
                return res.handler.success("app_settings_updated");
            }

            /* body.tenant_id = body.tenantId;
            await TenantPortalModal.updateTenantOrderPrefix(body, req.headers);
            const configurationAppSetting = await TenantPortalModal.configurationAppSetting(body.tenant_id, req.headers);
            await configurationAppSetting.save();

            return res.handler.success("added_app_settings") */
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async allowNewValidation(req, res) {  // Dec 30 users and customer limit validation
        try {
            const tenantId = Number(req.query.tenantId);
            const type = req.query.type;
            let data = {}
            const defaultTenantSetting = await SystemPortalModal.getTenantDetailsById(tenantId)

            if (type === TENANT_ALLOW_VALIDATION.CUSTOMER) {
                data["activeCustomers"] = await TenantPortalModal.allowNewValidation(tenantId, type)
                let advanceLimit = defaultTenantSetting.advance_limit.find(data => data.key === 'NUMBER_OF_CUSTOMERS');
                data["allowCustomers"] = advanceLimit.allowance
            } else if (type === TENANT_ALLOW_VALIDATION.USER) {
                data["activeTenantUsers"] = await TenantPortalModal.allowNewValidation(tenantId, type)
                let advanceLimit = defaultTenantSetting.advance_limit.find(data => data.key === 'NUMBER_OF_USERS');
                data["allowTenantUsers"] = advanceLimit.allowance
            }
            return res.handler.success(null, data)
        }
        catch (error) {
            return res.handler.serverError(error)
        }
    }  // Dec 30 users and customer limit validation

    async getUserSettings(req, res) {
        try {
            const userRoleId = req.headers.userroleid;
            const { tenantId } = req.query;

            const userRoleDetails = await TenantPortalModal.findUserProfileWithFilter(
                {
                    _id: userRoleId,
                    is_deleted: false
                },
                {
                    allow_price_change: 1,
                    role_id: 1,
                    preferred_language: 1,
                },
                toLeanOption
            )

            if (!userRoleDetails) {
                return res.handler.notFound("invalid_user_role");
            }

            let [userSettings, tenantAppSettings] = await Promise.all([
                TenantPortalModal.findUserRoleSetting({ _id: `${tenantId}_${userRoleId}` }, excludeProjections, toLeanOption),
                TenantPortalModal.tenantAppSettingExist(tenantId, excludeProjections, toLeanOption),
            ]);

            if (!userSettings) {
                const userRoleType = await RoleModal.getRoleByFilter(
                    {
                        _id: userRoleDetails.role_id,
                        is_deleted: false,
                    },
                    {
                        portal_type: 1,
                    },
                    toLeanOption
                )

                userSettings = {
                    _id: `${tenantId}_${userRoleId}`,
                    tenant_id: tenantId,
                    user_role_id: userRoleId,
                    out_of_stock: {
                        visible: false,
                        searchable: false,
                    },
                    price_change: false,
                    preferred_language:
                        userRoleType?.portal_type === VALUES.portals.CUSTOMER_APP
                            ? userRoleDetails.preferred_language
                            : LANGUAGE.EN,
                };

                await TenantPortalModal.updateUserRoleSetting(
                    { _id: userSettings._id },
                    { $set: userSettings },
                    { upsert: true, new: true }
                );
            }

            return res.handler.success(null, { userSettings: userSettings, tenantAppSettings, allowPriceChange: userRoleDetails.allow_price_change });
        } catch (error) {
            return res.handler.serverError(error)
        }
    }

    async updateUserSetting(req, res) {
        try {
            const userRoleId = req.headers.userroleid;
            let { masterPriceId, out_of_stock, priceChange, preferredLanguage, tenantId } = req.body;
            let userSetting = {};

            const [userRoleDetails, tenantDetails] = await Promise.all([
                TenantPortalModal.findUserProfileWithFilter(
                    {
                        _id: userRoleId,
                        is_deleted: false,
                    },
                    {
                        _id: 1,
                        tenant_id: 1,
                        role_id: 1,
                    },
                    toLeanOption
                ),
                TenantPortalModal.getTenantByFilter(
                    {
                        _id: tenantId,
                        is_deleted: false,
                    },
                    { _id: 1 },
                    toLeanOption
                ),
            ]);

            if (!tenantDetails) return res.handler.notFound("tenant_not_found");

            if (!userRoleDetails) return res.handler.notFound("invalid_user_role");

            userSetting['_id'] = `${tenantDetails._id}_${userRoleDetails._id}`;
            userSetting['tenant_id'] = tenantId;
            userSetting['user_role_id'] = userRoleDetails._id;
            userSetting["default_master_price_id"] = masterPriceId;
            userSetting["out_of_stock"] = out_of_stock;
            userSetting["price_change"] = priceChange;
            userSetting["preferred_language"] = preferredLanguage;

            const [, userRoleType] = await Promise.all([
                TenantPortalModal.updateUserRoleSetting(
                    { _id: userSetting._id },
                    userSetting,
                    { upsert: true }
                ),
                RoleModal.getRoleByFilter(
                    {
                        _id: userRoleDetails.role_id,
                        is_deleted: false,
                    },
                    {
                        _id: 1,
                        portal_type: 1,
                    },
                    toLeanOption,
                )
            ])

            if (userRoleType?.portal_type === VALUES.portals.CUSTOMER_APP) {
                await TenantPortalModal.updateUserRole(
                    { _id: userRoleId },
                    { $set: { preferred_language: preferredLanguage } },
                    { new: true }
                )
            }
            return res.handler.success("update_user_settings")
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async dataSheet(req, res) {
        try {
            const {
                tenantId,
                dataType,
                operationType,
                fileType,
                updateType,
                customFields,
                apiVersion,
            } = req.body

            if (
                [
                    DATA_SHEET.DATA_TYPE.PRODUCT,
                    DATA_SHEET.DATA_TYPE.PRICE,
                    DATA_SHEET.DATA_TYPE.INVENTORY,
                ].includes(dataType)
            ) {
                const payload = {
                    tenantId,
                    dataType,
                    operationType,
                    fileType,
                    updateType,
                    apiVersion,
                }
                const excelFile = await Api.get(req, "dataSheet", payload)

                if (
                    operationType === DATA_SHEET.OPERATION_TYPE.UPDATE &&
                    dataType === DATA_SHEET.DATA_TYPE.PRODUCT
                ) {
                    await DataSheetModel.sendSuccessProcessEmail(req, { excelFile })
                }

                return res.handler.success(
                    `${dataType.toLowerCase()}_export_${operationType.toLowerCase()}_success`,
                    excelFile
                )
            }

            const salesPersons = await TenantPortalModal.tenantSalesPersons(tenantId)
            const tenantMasterPriceList = await TenantPortalModal.tenantMasterPriceList(req)
            const arraySalesPersons = [];
            for (let index = 0; index < salesPersons.length; index++) {
                let salesPerson = {}
                salesPerson['salesPersonId'] = salesPersons[index]._id
                salesPerson['name'] = salesPersons[index].user_id.first_name + " " + salesPersons[index].user_id.last_name;
                salesPerson['mobileNumber'] = salesPersons[index].user_id.mobile_number;
                salesPerson['email'] = salesPersons[index].user_id.email;
                salesPerson['needToAppendMobileNumber'] = false

                arraySalesPersons.push(salesPerson)
            }

            for (let index = 0; index < arraySalesPersons.length; index++) {
                const newArray = arraySalesPersons.filter(item => item.name === arraySalesPersons[index].name && (item.email != arraySalesPersons[index].email || item.mobileNumber != arraySalesPersons[index].mobileNumber));
                if (newArray.length > 0) {
                    arraySalesPersons[index].needToAppendMobileNumber = true
                }
                if (arraySalesPersons[index].needToAppendMobileNumber) {
                    arraySalesPersons[index]["salespersonName"] = arraySalesPersons[index].name + " " + `(${arraySalesPersons[index].mobileNumber})`
                } else {
                    arraySalesPersons[index]["salespersonName"] = arraySalesPersons[index].name
                }
            }

            if (dataType === DATA_SHEET.DATA_TYPE.CUSTOMER && operationType === DATA_SHEET.OPERATION_TYPE.CREATE) {
                const customerColumnList = await TenantPortalModal.columnField({ module: "CUSTOMER_CREATE" })
                const excelFile = await TenantPortalModal.customerExcel(req, tenantId, arraySalesPersons, tenantMasterPriceList, customerColumnList.fields)
                return res.handler.success("customer_export_create", excelFile)
            } else if (dataType === DATA_SHEET.DATA_TYPE.CUSTOMER && operationType === DATA_SHEET.OPERATION_TYPE.UPDATE) {
                const customerColumnList = await TenantPortalModal.columnField({ module: "CUSTOMER" })
                const newElement = "Customer Id"
                const newArray = [newElement].concat(customerColumnList.fields)
                const excelFile = await TenantPortalModal.customerExcel(req, tenantId, arraySalesPersons, tenantMasterPriceList, newArray)
                return res.handler.success("customer_export_update", excelFile)
            } else if (dataType === DATA_SHEET.DATA_TYPE.CUSTOMER && operationType === DATA_SHEET.OPERATION_TYPE.CUSTOM_FIELDS) {
                const excelFile = await TenantPortalModal.customerExcel(req, tenantId, arraySalesPersons, tenantMasterPriceList, customFields)
                return res.handler.success("customer_export_custom_field", excelFile)
            }
        }
        catch (error) {
            try {
                await DataSheetModel.sendExportFailEmail(req, error.message)
            }
            catch (err) {
                logger.error(err, {
                    errorMessage: "Error: (DataSheetModel/sendExportFailEmail) Failed to send email"
                })
            }
            return res.handler.serverError(error);
        }
    }

    async columnField(req, res) {
        try {
            const columnFieldList = await TenantPortalModal.columnField({ module: req.query.dataType })
            return res.handler.success(null, columnFieldList)
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getUploadSignature(req, res) {
        try {
            let {
                dataType,
                operationType,
                fileName,
                updateType,
            } = req.body;

            const tenantId = Number(req.body.tenantId);

            const publicS3 = new FileUpload(BUCKET_TYPE.PUBLIC);
            const isUpdateTypeStatus = updateType === DATA_SHEET.UPDATE_TYPE.STATUS
            let filePath = "import"

            if (isUpdateTypeStatus) {
                filePath += "/" + DATA_SHEET.UPDATE_TYPE.STATUS.toLowerCase()
            }

            const pathName = FILE_PATH.DATA_SHEET_CUSTOMER_EXPORT_SHEET + `/${tenantId}/${dataType.toLowerCase()}/${operationType.toLowerCase()}/${filePath}`;
            const responseData = {
                signedUrl: "",
                fileName: ""
            };

            if (fileName) {
                responseData["fileName"] = fileName;
            }
            else {
                let extension = "xlsx"

                if (
                    [
                        DATA_SHEET.DATA_TYPE.PRICE,
                        DATA_SHEET.DATA_TYPE.INVENTORY
                    ].includes(dataType) ||

                    isUpdateTypeStatus
                ) {
                    extension = "csv"
                }
                fileName = `${dataType.toLowerCase()}_${new Date().toISOString().split('T')[0]}_${new Date().getTime()}.${extension}`

                responseData["fileName"] = fileName;
            }

            responseData["signedUrl"] = await publicS3.getPutObject(pathName, fileName);
            return res.handler.success(null, responseData)
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async updateUploadFile(req, res) {
        try {
            const { fileName, dataType, operationType, isReUpload = false, originalFileName } = req.body;
            const updateFile = await TenantPortalModal.updateExcelFile(req)
            if (!updateFile) {
                return res.handler.badRequest("invalid_file")
            }

            let excelFileRecord;
            let message;
            if (isReUpload) {
                excelFileRecord = await TenantPortalModal.getFileByName(fileName)

                if (!excelFileRecord || excelFileRecord.is_deleted) {
                    res.handler.notFound("excel_file_not_found")
                }
                excelFileRecord.original_file_name = originalFileName;
                excelFileRecord.status = DATA_SHEET.STATUS.FOR_REVIEW
                excelFileRecord.updated_by = req.headers.userDetails._id

                await excelFileRecord.save();

                message = "import_reupload_file"

            } else {
                excelFileRecord = await TenantPortalModal.createExcelFileRecord(req.body, req.headers)
                await excelFileRecord.save()

                message = `${dataType.toLowerCase()}_import_${operationType.toLowerCase()}`
            }

            await DataSheetModel.sendUploadSuccessEmail(req)

            return res.handler.success(message, excelFileRecord)
        }
        catch (error) {
            try {
                await DataSheetModel.sendUploadFailEmail(req, error.message)
            }
            catch (err) {
                logger.error(err, {
                    errorMessage: "Error: (DataSheetModel/sendUploadFailEmail) Failed to send email"
                })
            }
            return res.handler.serverError(error);
        }
    }

    async getDataSheetList(req, res) {
        try {
            const body = req.query;
            // body.tenantId = Number(body.tenantId);
            const result = await TenantPortalModal.getDataSheetList(body, req.headers);
            return res.handler.success(null, result);
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async validation(
        req,
        body,
        tenantInfo,
        salesPersons,
        activeCustomers,
        tenantAppSetting,
        tenantMasterPriceList,
        customerRole,
        country,
        operationType,
        fields,
        excelTitles,
    ) {
        const tenantId = Number(req.body.tenantId);
        const gps_coordinates = { longitude: body.gps_coordinates_longitude === '' ? 0 : body.gps_coordinates_longitude, latitude: body.gps_coordinates_latitude === '' ? 0 : body.gps_coordinates_latitude }

        if ("customer_mobile_number" in body) {
            // Removing extra spaces from the mobile number
            body.customer_mobile_number = removeAllWhitespace(body.customer_mobile_number)
        }

        if ("shipping_mobile_number" in body) {
            body.shipping_mobile_number = removeAllWhitespace(body.shipping_mobile_number)
        }

        // Extracting the customer email value if exists, from the formula object
        if ("customer_email" in body) {
            if (typeof body.customer_email === "string") {
                body.customer_email = removeAllWhitespace(body.customer_email)
            }
            else if (typeof body.customer_email === "object") {
                if (body.customer_email.text) {
                    body.customer_email = removeAllWhitespace(body.customer_email.text)
                }
            }
        }

        const restData = {
            tenantInfo,
            salesPersons,
            activeCustomers,
            tenantAppSetting,
            tenantMasterPriceList,
            customerRole,
            country,
            operationType,
            fields,
            excelTitles,
        }

        const newTenantPortalModel = new (require("../Models/tenantPortal"))()
        const CustomerDataSheetInstance = new CustomerDataSheetModel(newTenantPortalModel)

        const customerValidation = await CustomerDataSheetInstance.checkValidation(
            req,
            body,
            restData,
        )

        if (customerValidation.length) {
            return customerValidation
        }

        try {
            let region
            let city

            if ("shipping_region" in body && body.shipping_region) {
                if (mongoose.isValidObjectId(body.shipping_region)) {
                    region = await CommonModal.getRegionById(body.shipping_region)
                }
                else {
                    region = await CommonModal.findRegionByName(body.shipping_region)
                }
            }

            if ("shipping_city" in body && body.shipping_city) {
                if (mongoose.isValidObjectId(body.shipping_city)) {
                    city = await CommonModal.getCityById(body.shipping_city)
                }
                else {
                    city = await CommonModal.findCityByName(body.shipping_city)
                }
            }

            let userDetails;
            let newUser = false;

            if ("customer_mobile_number" in body) {
                const existingCustomer = await TenantPortalModal.findTenantCustomerByMobile(tenantInfo.country_code, body.customer_mobile_number)
                userDetails = existingCustomer
            }

            if (!userDetails) {
                newUser = true;
            }

            let userRole;

            if ("external_id" in body) {
                body.external_id = String(body.external_id).trim()

                if (FALSELY_VALUES.includes(body.external_id)) {
                    body.external_id = ""
                }
            }

            if (
                [
                    DATA_SHEET.OPERATION_TYPE.UPDATE,
                    DATA_SHEET.OPERATION_TYPE.CUSTOM_FIELDS
                ].includes(operationType)
            ) {
                const userRoleInfo = await TenantPortalModal.findCustomerWithCustomerId(body.customer_id)

                if (!userDetails && body.customer_mobile_number) {
                    userDetails = await TenantPortalModal.addTenantCustomer();
                    userDetails.country_code = tenantInfo.country_code;
                    userDetails.mobile_number = body.customer_mobile_number;
                    userDetails.created_by = req.headers.userDetails._id;
                    userDetails.updated_by = req.headers.userDetails._id;
                    userDetails.unique_mobile_number = userDetails.country_code + userDetails.mobile_number;

                    userRoleInfo.user_id = userDetails._id;
                    await userDetails.save();
                }

                userRoleInfo.tenant_id = tenantId;
                userRoleInfo.branch_id = body.branchId;
                userRoleInfo.role_id = customerRole._id;
                userRoleInfo.shipping_country_id = tenantInfo.country;
                userRoleInfo.updated_by = req.headers.userDetails._id;

                if (operationType === DATA_SHEET.OPERATION_TYPE.CUSTOM_FIELDS) {
                    await CustomerDataSheetInstance.updateCustomer(userRoleInfo, body, region, city, tenantInfo)
                    return
                }

                userRoleInfo.sales_person_id = body.sales_person;
                userRoleInfo.customer_app_access = body.customer_app_access === true ? true : false;
                userRoleInfo.is_active = body.is_active === true ? true : false;

                userRoleInfo.preferred_language = body.preferred_language;
                userRoleInfo.external_id = body.external_id;

                if (body.external_id) {
                    userRoleInfo.unique_external_id = `${tenantId}_${body.external_id}`
                }
                else {
                    userRoleInfo.unique_external_id = null
                }

                userRoleInfo.customer_name = body.customer_name;
                userRoleInfo.customer_legal_name = body.customer_legal_name;
                userRoleInfo.shipping_address = body.shipping_address;
                userRoleInfo.shipping_city_id = city ? city._id : null;
                userRoleInfo.shipping_region_id = region ? region._id : null;
                userRoleInfo.shipping_country_code = body.shipping_mobile_number === "" ? "" : tenantInfo.country_code;
                userRoleInfo.shipping_mobile_number = body.shipping_mobile_number;
                userRoleInfo.gps_coordinates = gps_coordinates;
                userRoleInfo.price_list_id = body.price_list;
                userRoleInfo.customer_catalog_mode = body.catalog_mode === true ? true : false;

                userRoleInfo.customer_email = body.customer_email;
                userRoleInfo.customer_first_name = body.customer_first_name;
                userRoleInfo.customer_last_name = body.customer_last_name;

                await userRoleInfo.save();
                return
            }

            //NOTE: can create multiple customer with same primary contact number with same salesperson
            /*if (userDetails) {
                userRole = await TenantPortalModal.findUserProfileWithFilter({ tenant_id: tenantId, user_id: userDetails._id, role_id: customerRole._id });
            } */

            if (userRole) {
                if (userRole.is_deleted) {

                    userRole.is_deleted = false;
                    userRole.tenant_id = tenantId;
                    userRole.role_id = customerRole._id;
                    userRole.sales_person_id = body.sales_person;
                    userRole.customer_app_access = body.customer_app_access === true ? true : false
                    userRole.is_active = body.is_active === true ? true : false;
                    userRole.preferred_language = body.preferred_language;
                    userRole.external_id = body.external_id

                    if (body.external_id) {
                        userRole.unique_external_id = `${tenantId}_${body.external_id}`
                    }
                    else {
                        userRole.unique_external_id = null
                    }

                    userRole.customer_name = body.customer_name
                    userRole.customer_legal_name = body.customer_legal_name
                    userRole.shipping_address = body.shipping_address;
                    userRole.shipping_country_id = tenantInfo.country;
                    userRole.shipping_region_id = region ? region._id : null;
                    userRole.shipping_city_id = city ? city._id : null;
                    userRole.shipping_country_code = tenantInfo.country_code;
                    userRole.shipping_mobile_number = body.shipping_mobile_number;
                    userRole.gps_coordinates = gps_coordinates;
                    userRole.price_list_id = body.price_list;
                    userRole.is_verified = /*body.isVerified ||*/ false
                    userRole.collection_name = "tenant_customers"

                    userRole.user_id = userDetails._id;
                    userRole.customer_email = body.customer_email;
                    userRole.customer_first_name = body.customer_first_name;
                    userRole.customer_last_name = body.customer_last_name;

                    await userRole.save();
                    // message = "restored_user"

                } else {
                    // customerValidation.push('Customer already exists')

                    return customerValidation
                    // return res.handler.conflict("VALIDATION.EXISTS.CUSTOMER")
                }
            }
            else {
                var customerCount = tenantInfo.customer_counter += 1
                if (newUser) {
                    userDetails = await TenantPortalModal.addTenantCustomer();
                    userDetails.country_code = tenantInfo.country_code;
                    userDetails.mobile_number = body.customer_mobile_number;
                    userDetails.created_by = req.headers.userDetails._id;
                    userDetails.updated_by = req.headers.userDetails._id;
                    userDetails.unique_mobile_number = userDetails.country_code + userDetails.mobile_number;

                    await userDetails.save();
                }

                body.tenant_id = tenantId;
                // body.branch_id= body.branchId;
                body.user_id = userDetails._id;
                body.customer_id = '' + tenantId + customerCount;
                body.role_id = customerRole._id;
                body.sales_person_id = body.sales_person;
                body.customer_app_access = body.customer_app_access === true ? true : false
                body.is_active = true;
                body.preferred_language = body.preferred_language;
                body.external_id = body.external_id

                if (body.external_id) {
                    body.unique_external_id = `${tenantId}_${body.external_id}`
                }

                body.customer_name = body.customer_name
                body.customer_legal_name = body.customer_legal_name
                body.shipping_address = body.shipping_address;
                body.shipping_country_id = tenantInfo.country;
                body.shipping_city_id = city ? city._id : null;
                body.shipping_region_id = region ? region._id : null;
                body.shipping_country_code = body.shipping_mobile_number === "" ? "" : tenantInfo.country_code;
                body.shipping_mobile_number = body.shipping_mobile_number;
                body.gps_coordinates = gps_coordinates;
                body.price_list_id = body.price_list;
                body.is_verified = false;
                body.customer_catalog_mode = body.catalog_mode === true ? true : false;
                body.is_deleted = false;
                body.user_id = userDetails._id;
                body.customer_email = body.customer_email;
                body.customer_first_name = body.customer_first_name;
                body.customer_last_name = body.customer_last_name;
                body.created_by = req.headers.userDetails._id;
                body.updated_by = req.headers.userDetails._id;
                body.collection_name = "tenant_customers";

                // userRole = await TenantPortalModal.addCustomer(body, req.headers);

                userRole = await TenantPortalModal.addCustomerProfile(body);
                // message = "ADDED.CUSTOMER"
            }
            // if (userRole.customer_app_access) {
            //     userRole.customer_app_request = false;
            // } else {
            //     userRole.customer_app_request = body.customerAppRequest ? body.customerAppRequest : false;
            // }
            await Promise.all([
                userRole.save(),
                tenantInfo.save()
            ]);

            return
        }
        catch (error) {
            if (error.code === STATUS_CODES.MONGODB_DUPLICATE_KEY_CODE) {
                if ("unique_external_id" in error.keyValue) {
                    customerValidation.push("External id already exists")
                }
                else if ("unique_mobile_number" in error.keyValue) {
                    customerValidation.push("Mobile number already exists")
                }
                else if ("customer_id" in error.keyValue) {
                    customerValidation.push("Customer id already exists")
                }
                else {
                    customerValidation.push(error.message)
                }
            } else {
                customerValidation.push(error.message)
            }

            return customerValidation
        }
    }

    async importValidation(req, res) {
        try {
            const {
                tenantId,
                fileId,
                approveType,
                selectRow,
                apiVersion,
            } = req.body

            const sheetsPath = path.join(__dirname, "/../Assets/Datasheets/")

            const getFile = await TenantPortalModal.getFile(fileId)

            getFile.process_start_time = new Date().toISOString();
            getFile.status = DATA_SHEET.STATUS.IN_PROGRESS;
            getFile.original_file_name = getFile.original_file_name || getFile.file_name;
            getFile.updated_by = req.headers.userDetails._id

            await getFile.save();

            if (!getFile || getFile.is_deleted) {
                res.handler.notFound("excel_file_not_found")
            }

            if (getFile.data_type === DATA_SHEET.DATA_TYPE.PRODUCT) {
                const payload = {
                    tenantId,
                    fileName: getFile.file_name,
                    dataType: DATA_SHEET.DATA_TYPE.PRODUCT,
                    operationType: getFile.operation_type,
                    updateType: getFile.update_type || undefined,
                    type: DATA_SHEET.TYPE.IMPORT,
                    importData: true,
                    fileId,
                    approveType,
                    selectRow,
                    apiVersion,
                }
                const updateData = await Api.put(req, "dataSheet", payload)

                if (!updateData) {
                    return res.handler.custom(STATUS_CODES.NOT_VALID_DATA, "invalid_file")
                }

                await DataSheetModel.sendValidationSuccessProcessEmail(req, payload)

                if (approveType === DATA_SHEET.APPROVE_TYPE.ALL) {
                    return res.handler.success("validation_checked")
                } else {
                    return res.handler.success("select_validation_checked")
                }
            }
            else if (getFile.data_type === DATA_SHEET.DATA_TYPE.PRICE) {
                const payload = {
                    tenantId,
                    fileName: getFile.file_name,
                    dataType: DATA_SHEET.DATA_TYPE.PRICE,
                    operationType: getFile.operation_type,
                    type: DATA_SHEET.TYPE.IMPORT,
                    importData: true,
                    fileId,
                    approveType,
                    selectRow,
                    apiVersion,
                }
                const updateData = await Api.put(req, "dataSheet", payload)

                if (!updateData) {
                    return res.handler.custom(STATUS_CODES.NOT_VALID_DATA, "invalid_file")
                }

                await DataSheetModel.sendValidationSuccessProcessEmail(req, payload)

                if (approveType === DATA_SHEET.APPROVE_TYPE.ALL) {
                    return res.handler.success("validation_checked")
                } else {
                    return res.handler.success("select_validation_checked")
                }
            }
            else if (getFile.data_type === DATA_SHEET.DATA_TYPE.INVENTORY) {
                const payload = {
                    tenantId,
                    fileName: getFile.file_name,
                    dataType: DATA_SHEET.DATA_TYPE.INVENTORY,
                    operationType: getFile.operation_type,
                    type: DATA_SHEET.TYPE.IMPORT,
                    importData: true,
                    fileId,
                    approveType,
                    selectRow,
                    apiVersion,
                }
                const updateData = await Api.put(req, "dataSheet", payload)

                if (!updateData) {
                    return res.handler.custom(STATUS_CODES.NOT_VALID_DATA, "invalid_file")
                }

                await DataSheetModel.sendValidationSuccessProcessEmail(req, payload)

                if (approveType === DATA_SHEET.APPROVE_TYPE.ALL) {
                    return res.handler.success("validation_checked")
                } else {
                    return res.handler.success("select_validation_checked")
                }
            }

            let i;
            i = 65;
            var firstArr = [""];
            var secondArr = [];
            while (i <= 90) {
                firstArr.push(String.fromCharCode(i))
                secondArr.push(String.fromCharCode(i))
                i++;
            }

            var thirdArr = []

            for (let index = 0; index < firstArr.length; index++) {
                const element = firstArr[index];

                for (let index = 0; index < secondArr.length; index++) {
                    const secondElement = secondArr[index];
                    const key = `${element}` + `${secondElement}`
                    thirdArr.push(key) // ['A', 'B', 'C']
                }
            }

            const s3FilePath = process.env.AWS_PUBLIC_BUCKET_BASE_URL + `dataSheet/${tenantId}/${getFile.data_type.toLowerCase()}/${getFile.operation_type.toLowerCase()}/import/${getFile.file_name}`

            await TenantPortalModal.tempExcelFile(s3FilePath, `${getFile.file_name}`)
            let workbook = new excelJS.Workbook();
            workbook = await workbook.xlsx.readFile(`${sheetsPath}/${getFile.file_name}`)
            let excelTitles = [];
            let worksheet;
            if (getFile.data_type === DATA_SHEET.DATA_TYPE.CUSTOMER) {
                worksheet = workbook.getWorksheet("customer");

                const fields = {
                    "External id": "external_id",
                    "Customer Name": "customer_name",
                    "Legal Name": "customer_legal_name",
                    "Sales Person": "sales_person",
                    "Price List": "price_list",
                    "First Name": "customer_first_name",
                    "Last Name": "customer_last_name",
                    "Mobile Number": "customer_mobile_number",
                    "Email": "customer_email",
                    "Customer App Access": "customer_app_access",
                    "Catalog Mode": "catalog_mode",
                    "Preferred Language": "preferred_language",
                    "Street Address": "shipping_address",
                    // "shipping_country",
                    // TODO: STAGING TICKET 233 & 241
                    "Region": "shipping_region",
                    "City": "shipping_city",
                    "GPS Coordinate Lat": "gps_coordinates_latitude",
                    "GPS Coordinate Long": "gps_coordinates_longitude",
                    "Active": "is_active",
                    "Shipping Mobile Number": "shipping_mobile_number",
                    "Status": "status",
                    "Validations": "validations"
                }

                if (
                    [
                        DATA_SHEET.OPERATION_TYPE.UPDATE,
                        DATA_SHEET.OPERATION_TYPE.CUSTOM_FIELDS
                    ].includes(getFile.operation_type)
                ) {
                    fields["Customer Id"] = "customer_id"
                }

                let excelData = []; // [{ 'customer_id': '111910030', customer_app_access: true, shipping_mobile_number: 89891211212121, .... }, ....]
                // excel to json converter (only the first sheet)
                worksheet.eachRow((row, rowNumber) => {
                    // rowNumber 0 is empty
                    if (rowNumber > 0) {
                        // get values from row
                        let rowValues = row.values;
                        // remove first element (extra without reason)
                        rowValues.shift();
                        // titles row
                        if (rowNumber === 1) excelTitles = rowValues;
                        // table data
                        else {
                            // create object with the titles and the row values (if any)
                            let rowObject = {}

                            for (let i = 0; i < excelTitles.length; i++) {
                                const title = fields[excelTitles[i]];
                                const value = rowValues[i] ?? '';

                                rowObject[title] = value;
                            }
                            excelData.push(rowObject);
                        }
                    }
                })

                const [tenantInfo, salesPersons, activeCustomers, tenantAppSetting] = await Promise.all([
                    TenantPortalModal.getTenantById(tenantId),
                    TenantPortalModal.tenantSalesPersons(tenantId),
                    TenantPortalModal.allowNewValidation(tenantId, TENANT_ALLOW_VALIDATION.CUSTOMER),
                    TenantPortalModal.tenantAppSettingExist(tenantId)
                ]);

                const tenantMasterPriceList = await TenantPortalModal.tenantMasterPriceList(req)
                const customerRole = await RoleModal.getRoleByFilter({ name: "Customer", portal_type: VALUES.portals.CUSTOMER_APP, is_active: true, is_deleted: false }, { _id: 1 });
                const country = await CommonModal.findCountryById(tenantInfo.country)

                if (approveType === DATA_SHEET.APPROVE_TYPE.ALL) {
                    for (let index = 0; index < excelData.length; index++) {
                        if (excelData[index].status === DATA_SHEET.SHEET_STATUS.PENDING) {
                            const validation = await this.validation(
                                req,
                                excelData[index],
                                tenantInfo,
                                salesPersons,
                                activeCustomers,
                                tenantAppSetting,
                                tenantMasterPriceList,
                                customerRole,
                                country,
                                getFile.operation_type,
                                fields,
                                excelTitles,
                            )

                            if (validation) {
                                const stringValidation = [...new Set(validation)].toString();
                                worksheet.getCell(thirdArr[worksheet.actualColumnCount - 1] + (index + 2)).value = stringValidation;
                            }
                            else {
                                worksheet.getCell(thirdArr[worksheet.actualColumnCount - 2] + (index + 2)).value = DATA_SHEET.SHEET_STATUS.COMPLETE;
                                worksheet.getCell(thirdArr[worksheet.actualColumnCount - 1] + (index + 2)).value = "Passed Validation";
                            }
                        }
                    }
                } else if (approveType === DATA_SHEET.APPROVE_TYPE.SELECTED) {
                    const selectRowArray = selectRow
                    for (let index = 0; index < selectRowArray.length; index++) {
                        const rowNo = selectRowArray[index]
                        if (excelData[rowNo - 2].status === DATA_SHEET.SHEET_STATUS.PENDING) {
                            const validation = await this.validation(
                                req,
                                excelData[rowNo - 2],
                                tenantInfo,
                                salesPersons,
                                activeCustomers,
                                tenantAppSetting,
                                tenantMasterPriceList,
                                customerRole,
                                country,
                                getFile.operation_type,
                                fields,
                                excelTitles,
                            )

                            if (validation) {
                                const stringValidation = [...new Set(validation)].toString();
                                worksheet.getCell(thirdArr[worksheet.actualColumnCount - 1] + (rowNo)).value = stringValidation;
                            }
                            else {
                                worksheet.getCell(thirdArr[worksheet.actualColumnCount - 2] + (rowNo)).value = DATA_SHEET.SHEET_STATUS.COMPLETE;
                                worksheet.getCell(thirdArr[worksheet.actualColumnCount - 1] + (rowNo)).value = "Passed Validation";
                            }
                        }
                    }
                }
            }

            const columnStatus = worksheet.getColumn(worksheet.actualColumnCount - 1).values
            let isPending = false;

            for (let index = 0; index < columnStatus.length; index++) {
                if (columnStatus[index] == DATA_SHEET.SHEET_STATUS.PENDING) {
                    isPending = true;
                    break
                }
            }
            getFile.process_start_time = null;
            getFile.status = isPending ? DATA_SHEET.STATUS.FOR_REVIEW : DATA_SHEET.STATUS.COMPLETE;
            await getFile.save();

            await workbook.xlsx.writeFile(`${sheetsPath}/${getFile.file_name}`)

            const fileUpload = new FileUpload(BUCKET_TYPE.PUBLIC);
            await fileUpload.uploadFiles(FILE_PATH.DATA_SHEET_CUSTOMER_EXPORT_SHEET + `/${tenantId}/${getFile.data_type.toLowerCase()}/${getFile.operation_type.toLowerCase()}/import`, getFile.file_name, `${sheetsPath}/${getFile.file_name}`);

            const emailData = {
                dataType: getFile.data_type,
                operationType: getFile.operation_type,
                fileName: getFile.file_name,
            }
            await DataSheetModel.sendValidationSuccessProcessEmail(req, emailData)

            if (approveType === DATA_SHEET.APPROVE_TYPE.ALL) {
                return res.handler.success("validation_checked")
            } else {
                return res.handler.success("select_validation_checked")
            }
        }
        catch (error) {
            try {
                await DataSheetModel.sendValidationFailProcessEmail(req, error.message)
            }
            catch (err) {
                logger.error(err, {
                    errorMessage: "Error: (DataSheetModel/sendValidationFailProcessEmail) Failed to send email"
                })
            }
            return res.handler.serverError(error);
        }
    }

    async deleteDataSheet(req, res) {
        try {
            const body = req.body;
            body.tenantId = Number(body.tenantId);

            const dataSheets = await TenantPortalModal.findSheets(body);
            const path = []
            const fileName = []

            if (!dataSheets.length) {
                return res.handler.notFound("files_not_found")
            }

            dataSheets.forEach((obj) => {
                const filePath =
                    FILE_PATH.DATA_SHEET_CUSTOMER_EXPORT_SHEET + '/' +
                    obj.tenant_id + '/' +
                    obj.data_type.toLowerCase() + '/' +
                    obj.operation_type.toLowerCase() + '/' +
                    'import'

                path.push(filePath);
                fileName.push(obj.file_name)
            })

            const publicS3 = new FileUpload(BUCKET_TYPE.PUBLIC);
            const deleteFileResponse = await publicS3.deleteFiles(path, fileName)

            if (deleteFileResponse.status === 200) {
                dataSheets.forEach(async (dataSheet) => {
                    dataSheet.is_deleted = true;
                    dataSheet.updated_by = req.headers.userDetails._id
                    await dataSheet.save()
                })
            }
            return res.handler.success('delete_files_success', deleteFileResponse);
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async tenantsForImageMatch(req, res) {
        try {
            let arrLinkTenant;
            const array = [];
            const linkedTenants = await TenantPortalModal.tenantInfo({ _id: req.query.tenantId }, { linked_tenant_id: 1 })
            const eachOtherLinkedTenants = await TenantPortalModal.tenantInfo({ linked_tenant_id: req.query.tenantId }, { _id: 1 })
            eachOtherLinkedTenants.map(({ _id }) => array.push(_id))

            if (linkedTenants.length) {
                arrLinkTenant = linkedTenants[0].linked_tenant_id
            } else {
                arrLinkTenant = []
            }

            let newArr = [...arrLinkTenant, ...array]
            let uniqueArr = [...new Set(newArr)];

            const linkedTenantDetails = await TenantPortalModal.tenantInfo({ _id: { $in: uniqueArr } }, { name: 1 })
            return res.handler.success(null, { uniqueArr, linkedTenantDetails })
        } catch (error) {
            return res.handler.serverError(error);
        }
    }

    async allowPayment(req, res) {
        try {
            await TenantPortalModal.updateBulkCustomers(
                {
                    _id: {
                        $in: req.body.userRoleIds
                    }
                },
                {
                    is_payment_enabled: req.body.status,
                },
                req.headers
            );

            return res.handler.success("updated_edited")
        }
        catch (error) {
            await sendFailureEmail(req, error)
            return res.handler.serverError(error);
        }
    }

    async getCustomersForPayment(req, res) {
        try {
            const customers = await TenantPortalModal.getCustomersForPayment(req.query);
            return res.handler.success(null, customers)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async getCustomersForRewardProgram(req, res) {
        try {
            const customers = await TenantPortalModal.getCustomersForRewardProgram(req.query);
            return res.handler.success(null, customers)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async listPaymentTerms(req, res) {
        try {
            const data = await CustomerPaymentTermModel.listPaymentTerms()
            return res.handler.success(undefined, data)
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

    async scanQrCode(req, res) {
        try {
            const {
                qrInfo
            } = req.body

            const decryptedQr = EncryptionHandler.decrypt(qrInfo);
            const {
                status = "",
                data = "",
                error,
            } = decryptedQr || {};

            if (status === STATUS_CODES.SERVER_ERROR) {
                return res.handler.badRequest("unable_to_parse_qr", undefined, error)
            }

            const [
                type,
            ] = data.split("_")

            if (type === QR_CODE_TYPE.REWARD_PROGRAM_MEMBER) {
                return await RewardProgramPointController.scanQrCodeForMemberScan(req, res)
            }
            else {
                const {
                    statuscode,
                    data,
                    error
                } = await InternalServiceModal.standQrCodeScan(req)

                return res.handler.custom(
                    statuscode,
                    data?.message,
                    data?.data,
                    error
                )
            }
        }
        catch (error) {
            return res.handler.serverError(error);
        }
    }

}

module.exports = TenantPortalController;
