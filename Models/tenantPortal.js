const moment = require('moment');
const excelJS = require("exceljs");
const path = require('path');
const https = require('https');
const fs = require('fs');
const Stream = require('stream').Transform;

const {
    tenants: TenantSchema,
    tenant_branch: TenantBranchSchema,
    roles: RoleSchema,
    user_roles: UserRoleSchema,
    user_sessions: UserSessionSchema,
    users: UserSchema,
    tenant_pricelists: TenantPricelistSchema,
    shipping_labels: ShippingLabelSchema,
    tenant_app_settings: TenantAppSettingSchema,
    tenant_order_prefixes: TenantOrderPrefixSchema,
    tenant_customers: TenantCustomerSchema,
    column_fields: ColumnFieldSchema,
    tenant_datasheets: TenantDataSheetSchema,
    user_role_settings: UserRoleSettingSchema,
} = require('../Database/Schemas')

const CommonModal = new (require('../Models/common'))();
const RoleModal = new (require('./roles'));
const FileUpload = require('../Configs/awsUploader').S3Upload;
const Api = new (require("../Utils/Api"))()
const CustomerDataSheetModel = require('./CustomerDataSheetModel')

const {
    httpService,
    toLeanOption,
} = require('../Utils/helpers');

const {
    ADD_TENANT_SCREENS,
    VALUES, ENTITY_STATUS,
    LISTING_TYPES, CUSTOMER_TYPE,
    CUSTOMER_STATUS_TYPE,
    CUSTOMER_PAYMENT_FILTER_TYPE,
    CUSTOMER_REWARD_PROGRAM_FILTER_TYPE,
    TENANT_ALLOW_VALIDATION,
    FILE_PATH, BUCKET_TYPE,
    DATA_SHEET,
    REGEX,
    FALSELY_VALUES,
    SORT_TYPE,
    TENANT_USER_SORT_BY
} = require('../Configs/constants')


class TenantPortalModal {

    async getTenantByFilter(filter, projection, options = {}, projectionArray = []) {
        let query = TenantSchema.findOne(filter, projection, options);

        for (let i = 0; i < projectionArray.length; i++) {
            query = query.populate(projectionArray[i]);
        }

        if (options?.lean) {
            query = query.lean();
        }
        return query;
    }

    async findExistingTenantPortalProfile(tenant_id, user_id, tenantRoles) {
        return UserRoleSchema.findOne({ tenant_id, user_id, role_id: { $in: tenantRoles } }).populate({ path: 'role_id' });
    }

    async findUserProfileWithFilter(filter, projection, options = {}) {
        return UserRoleSchema.findOne(filter, projection, options);
    }

    async updateUserRole(filter, updateObject, options) {
        return UserRoleSchema.updateOne(filter, updateObject, options);
    }

    async updateUserProfilesWithFilter(filter, projection, options = {}) {
        return UserRoleSchema.updateMany(filter, projection, options);
    }

    async findUserProfilesWithFilter(filter, projection, options) {
        return UserRoleSchema.find(filter, projection, options);
    }

    async createTenantPortalProfile(body, header) {

        return UserRoleSchema({
            tenant_id: body.tenant_id,
            is_deleted: false,
            user_id: body.user_id,
            role_id: body.role_id,
            is_active: body.is_active,
            allow_all_permission: body.allow_all_permission,
            allow_price_change: body.allowPriceChange,
            notifications: body.notifications,
            created_by: header.userDetails._id,
            updated_by: header.userDetails._id,
            collection_name: "users",
        })
    }

    async findUserRoleSetting(filter, project, options) {
        return UserRoleSettingSchema.findOne(filter, project, options);
    }

    async findUserRoleSettings(filter, project, options) {
        return UserRoleSettingSchema.find(filter, project, options);
    }

    async getUserRoleSettingsCount(filter) {
        return UserRoleSettingSchema.countDocuments(filter);
    }

    async updateUserRoleSetting(filter, updateObject, options) {
        return UserRoleSettingSchema.updateOne(filter, updateObject, options);
    }

    async createUserRoleSetting(settingsData) {
        return new UserRoleSettingSchema(settingsData);
    }

    async findBranchById(branch_id, projection = {}, options = {}) {
        return TenantBranchSchema.findById(branch_id, projection, options);
    }

    async findExistingBranchPortalProfile(tenant_id, branch_id, user_id, branch_portal_roles) {
        return UserRoleSchema.findOne({ user_id, role_id: { $in: branch_portal_roles }, tenant_id, branch_id })
    }

    async findExistingSalesAppProfile(tenant_id, branch_id, user_id, sales_portal_roles) {
        const query = { user_id, role_id: { $in: sales_portal_roles }, tenant_id };
        if (branch_id) {
            query['branch_id'] = branch_id;
        }
        return UserRoleSchema.findOne(query);
    }

    async createBranchManager(body, header) {
        return UserRoleSchema({
            tenant_id: body.tenantId,
            branch_id: body.branchId,
            is_deleted: false,
            user_id: body.user_id,
            role_id: body.roleId,
            is_active: body.isActive,
            allow_price_change: body.allowPriceChange,
            notifications: body.notifications,
            created_by: header.userDetails._id,
            updated_by: header.userDetails._id,
            collection_name: "users",
        });
    }

    async findSupervisorRole(projection = {}) {
        return RoleSchema.findOne({ name: "Supervisor", portal_type: VALUES.portals.SUPERVISOR_APP }, projection)
    }

    async getSalesPersonRole(projection = {}, options = {}) {
        return RoleSchema.findOne({ name: "Sales Person", portal_type: VALUES.portals.SALES_APP }, projection, options)
    }

    async getCustomerRole(projection = {}, options = {}) {
        return RoleSchema.findOne({ name: "Customer", portal_type: VALUES.portals.CUSTOMER_APP }, projection, options);
    }

    async getTenantUsersWithRoleId(tenant_id, role_id, branch_id, is_active) {
        const query = {
            tenant_id,
            role_id,
            is_deleted: false
        }

        if (branch_id) {
            query.branch_id = branch_id
        }

        if (is_active !== undefined) {
            query.is_active = is_active
        }

        return UserRoleSchema
            .find(query)
            .populate({
                path: 'user_id',
                select: '_id first_name last_name is_active is_deleted'
            })
    }

    async getTenantUsers(filter = {}, projection = "", options = {}) {
        return UserRoleSchema.find(filter, projection, options)
    }

    async getTenantUsersCount(filter) {
        return UserRoleSchema.countDocuments(filter);
    }

    async usersDetailsWithIds(filter, projection = "", populateArray, options) {
        let query = UserRoleSchema.find(filter, projection, options)

        if (populateArray) {
            query = query.populate(populateArray)
        }
        return query
    }

    async findSuperVisorWithUserId(user_id, tenant_id, branch_id) {
        const supervisorRole = await this.findSupervisorRole();
        return UserRoleSchema.findOne({ user_id, tenant_id, role_id: supervisorRole._id }).populate({ path: 'user_id' }); // removed branch_id, supervisor is not related to any particular branch
    }

    async findSalesPersonWithUserRoleId(user_role_id, tenant_id, projection, options) {
        const SalesPersonRole = await this.getSalesPersonRole({ _id: 1 }, toLeanOption);

        return UserRoleSchema.findOne(
            {
                _id: user_role_id,
                tenant_id,
                role_id: SalesPersonRole._id,
            },
            projection,
            options,
        );
    }

    async findSalesPersonsWithBranchId(branch_id, role_id, projection = {}, options = {}) {
        let SalesPersonRole = role_id
        if (!SalesPersonRole) {
            SalesPersonRole = await this.getSalesPersonRole({ _id: 1 });
            SalesPersonRole = SalesPersonRole._id;
        }
        return UserRoleSchema.find({ role_id: SalesPersonRole, is_deleted: false, branch_id }, projection, options);
    }

    async findSalesPersonWithBranchIdWithPopulate(branch_id, role_id, projection = {}, options = {}, populationArray = []) {
        let SalesPersonRole = role_id
        if (!SalesPersonRole) {
            SalesPersonRole = await this.getSalesPersonRole({ _id: 1 });
            SalesPersonRole = SalesPersonRole._id;
        }

        let query = UserRoleSchema.find({ role_id: SalesPersonRole, is_deleted: false, branch_id }, projection, options);
        for (let i = 0; i < populationArray.length; i++) {
            const populateObj = populationArray[i];

            if (isEmpty(populateObj) || !populateObj.path) {
                throw new Error("object is missing path property");
            }

            query = query.populate({ ...populateObj });

        }

        if (options?.lean) {
            query.lean();
        }

        return query;
    }

    async updateSupervisorOfSales(supervisor_id, tenant_id, newSupervisorId) {
        const role = await this.getSalesPersonRole();
        return UserRoleSchema.updateMany({ supervisor_id, tenant_id, role_id: role._id }, { supervisor_id: newSupervisorId });
    }

    async updateCustomerSalesPerson(old_sales_person_id, tenant_id, newSalesPersonId,) {
        const customerRole = await this.getCustomerRole();
        return UserRoleSchema.updateMany({ role_id: customerRole._id, tenant_id, sales_person_id: old_sales_person_id }, { sales_person_id: newSalesPersonId })
    }

    async markSessionAsRoleChanged(user_role_id) {
        return UserSessionSchema.updateMany({ user_role_id, status: VALUES.sessionStatus.ACTIVE }, { has_role_changed: true })
    }

    async createSalesPerson(body, header) {
        return UserRoleSchema({
            tenant_id: body.tenantId,
            branch_id: body.branchId,
            is_deleted: false,
            user_id: body.user_id,
            role_id: body.roleId,
            is_active: body.isActive,
            allow_price_change: body.allowPriceChange,
            notifications: body.notifications,
            created_by: header.userDetails._id,
            updated_by: header.userDetails._id,
            supervisor_id: body.supervisorId,
            collection_name: "users",

        });
    }

    async createSupervisor(body, headers) {
        return UserRoleSchema({
            tenant_id: body.tenantId,
            // branch_id: body.branchId,   https://www.bugherd.com/projects/304712/tasks/83 ==> supervisor is not linked with any branch
            allow_all_permission: true,  // https://www.bugherd.com/projects/304712/tasks/83 ==> supervisor is not linked with any branch
            is_deleted: false,
            user_id: body.user_id,
            role_id: body.roleId,
            is_active: body.isActive,
            allow_price_change: body.allowPriceChange,
            notifications: body.notifications,
            created_by: headers.userDetails._id,
            updated_by: headers.userDetails._id,
            collection_name: "users",
        });
    }

    async findTenantAndBranchUsers(body, headers) {
        let {
            branchId = "",
            tenantId,
            searchKey = "",
            page = 0,
            perPage = 10,
            status = "",
            roleId = "",
            type = "",
            defaultRoles,
            sortBy,
            sortType,
        } = body;
        page = parseInt(page);
        perPage = parseInt(perPage);
        const offset = (page - 1) * perPage;

        const roles = roleId !== '' ? [new mongoose.Types.ObjectId(roleId)] : defaultRoles;
        const pipeline = [];
        pipeline.push(
            {
                $match: {
                    role_id: roles.length === 1 ? roles[0] : { $in: defaultRoles },
                    // list only valid profiles of the user
                    is_deleted: false,
                    tenant_id: tenantId
                }
            },
            {
                $lookup: {
                    from: 'users',
                    localField: 'user_id',
                    foreignField: '_id',
                    as: 'user',
                }
            },
            {
                $addFields: {
                    user: { $first: "$user" },
                },
            },
            {
                $sort: {
                    is_active: -1,
                    [sortBy]: sortType === SORT_TYPE.ASC ? 1 : -1
                }
            },
            {
                $lookup: {
                    from: 'roles',
                    localField: 'role_id',
                    foreignField: '_id',
                    as: 'role'
                }
            },
            {
                $lookup: {
                    from: 'tenant_branches',
                    localField: 'branch_id',
                    foreignField: '_id',
                    as: 'branch'
                }
            },
            {
                $addFields: {
                    role: { $first: "$role" },
                    branch: { $first: "$branch" }
                },
            },
        );

        if (searchKey) {
            pipeline.push(
                {
                    $addFields: {
                        mobileNumberStr: {
                            $toString: { $toLong: "$user.mobile_number" }
                        }
                    }
                },
                {
                    $match: {
                        $or: [
                            { "user.first_name": new RegExp(searchKey, "i") },
                            { "user.last_name": new RegExp(searchKey, "i") },
                            { "user.email": new RegExp(searchKey, "i") },
                            { "mobileNumberStr": { $regex: new RegExp(`.*${searchKey}.*`) } }
                        ]
                    }
                }
            )
        }

        if (status) {
            pipeline.push({
                $match: {
                    'is_active': status === ENTITY_STATUS.ACTIVE ? true : false
                }
            });
        }
        if (branchId) {
            pipeline.push({
                $match: {
                    $or: [
                        { "branch._id": new mongoose.Types.ObjectId(branchId) },
                        { allow_all_permission: true }
                    ]
                }
            });
        }

        pipeline.push(
            {
                $project: {
                    _id: 1,
                    role_id: 1,
                    is_deleted: 1,
                    allow_all_permission: 1,
                    is_active: 1,
                    branch_name: "$branch.name",
                    branch_id: "$branch._id",
                    // full_name: { $concat: [ "$user.first_name", " ", "$user.last_name" ] },
                    user_id: '$user._id',
                    first_name: '$user.first_name',
                    last_name: '$user.last_name',
                    email: '$user.email',
                    mobile_number: '$user.mobile_number',
                    // is_user_active: '$user.is_active',
                    user_role: '$role.name',
                    translate_key: "$role.translate_key",
                    country_code: '$user.country_code',

                }
            }
        )

        switch (type) {
            case LISTING_TYPES.SEARCH:
                return await UserRoleSchema.aggregate(pipeline)

            case LISTING_TYPES.PAGINATION:
                // not efficient but in future if we need to add other criteria the permutations and combination
                pipeline.push(
                    {
                        $facet: {
                            list: [{ $skip: offset }, { $limit: perPage }],
                            count: [{ $count: "count" }]
                        }
                    },
                    {
                        $unwind: "$count"
                    }
                );

                const data = await UserRoleSchema.aggregate(pipeline);
                const list = data.length ? data[0].list : [];
                const count = data.length ? data[0].count.count : 0;
                return { list, count };
                break;

            default:
                return await UserRoleSchema.aggregate(pipeline)
        }
    }

    async updateTenantUserRoleStatus(userRoleId, roles, status = ENTITY_STATUS.ACTIVE, headers) {
        return UserRoleSchema.updateMany({ _id: { $in: userRoleId }, role_id: { $in: roles } },
            {
                is_active: status === ENTITY_STATUS.ACTIVE ? true : false, updated_by: headers.userDetails._id
            });
    }

    async getTenantBranchPortalUserById(_id, projection = {}, options = {}) {
        return UserRoleSchema.findById(_id, projection, options)
            .populate({ path: 'user_id', select: '_id first_name last_name email mobile_number country_code profile_pic' })
            .populate({ path: 'role_id', select: '_id name portal_type permission' })
            .populate({ path: 'supervisor_id', select: '_id first_name last_name' })
    }

    async getOnlyUserRoleById(user_role_id, projection = {}, options = {}) {
        return UserRoleSchema.findById(user_role_id, projection, options)
    }
    async deleteTenantBranchPortalUser(_id) {
        return UserRoleSchema.updateMany({ _id }, { is_deleted: true });
    }

    async getPriceList(tenant_id) {
        return TenantPricelistSchema.find({ tenant_id });
    }

    async addTenantCustomer(object = {}) {
        return new TenantCustomerSchema(object);
    }

    async findTenantCustomerByMobile(country_code, mobile_number) {
        // return TenantCustomerSchema.findOne({ country_code, mobile_number, is_deleted: false });
        return TenantCustomerSchema.findOne({ unique_mobile_number: country_code + mobile_number, is_deleted: false }); // after adding unique index unique_mobile_number, search should be faster
    }

    async findTenantCustomer(filter, projection, options) {
        return TenantCustomerSchema.findOne(filter, projection, options);
    }

    async addCustomerProfile(customerProfile) {
        return new UserRoleSchema(customerProfile);
    }

    async addCustomer(body, header) {

        return UserRoleSchema({
            tenant_id: body.tenant_id,
            branch_id: body.branch_id,
            is_deleted: false,
            user_id: body.user_id,
            role_id: body.role_id,
            sales_person_id: body.sales_person_id,
            customer_id: body.customer_id,
            customer_app_access: body.customer_app_access,
            is_active: body.is_active,
            preferred_language: body.preferred_language,
            external_id: body.external_id,
            unique_external_id: body.external_id
                ? `${body.tenant_id}_${body.external_id}`
                : null,
            customer_name: body.customer_name,
            customer_legal_name: body.customer_legal_name,
            notifications: body.notifications,
            shipping_address: body.shipping_address,
            shipping_country_id: body.shipping_country_id,
            shipping_city_id: body.shipping_city_id,
            shipping_region_id: body.shipping_region_id,
            shipping_country_code: body.shipping_country_code,
            shipping_mobile_number: body.shipping_mobile_number,
            gps_coordinates: body.gps_coordinates,
            price_list_id: body.price_list_id,
            device_access: body.device_access,
            created_by: header.userDetails._id,
            updated_by: header.userDetails._id,
            collection_name: "tenant_customers",
        })
    }

    async findCustomerWithCustomerId(customerId) {
        return UserRoleSchema.findOne({ customer_id: customerId })
    }

    async getCustomer(_id, projection = "", options = {}, populate) {
        let query = UserRoleSchema.findById(new mongoose.Types.ObjectId(_id), projection, options)

        if (populate) {
            query = query.populate(populate)
        }
        return query
    }

    async getCustomerPortalUserById(_id, projection = {}, options = {}) {
        return UserRoleSchema.findById(_id, projection, options)
            .populate(
                {
                    path: 'user_id',
                    select: {

                        first_name: 1,
                        last_name: 1,
                        email: 1,
                        mobile_number: 1,
                        country_code: 1,
                        shipping_address: 1,
                        shipping_mobile_number: 1,
                        shipping_country_code: 1,
                        sales_person_id: 1,
                        shipping_country_id: 1,
                        shipping_city_id: 1,
                        shipping_region_id: 1,
                        gps_coordinates: 1,
                        is_payment_enabled: 1
                    },
                    populate: [
                        {
                            path: "shipping_country_id",
                            select: '_id name country_code'
                        },
                        {
                            path: "shipping_city_id",
                            select: '_id name',
                        },
                        {
                            path: "shipping_region_id",
                            select: '_id name',
                        }
                    ]
                })
            .populate({ path: 'role_id', select: '_id name portal_type' })
            .populate({ path: 'shipping_country_id', select: '_id name country_code' })
            .populate({ path: 'shipping_city_id', select: '_id name' })
            .populate({ path: 'shipping_region_id', select: '_id name' })
            // For ordering draft
            .populate({ path: "salesPersonDetail", select: { user_id: 1, branch_id: 1, _id: 0, collection_name: 1 }, populate: [{ path: "user_id", select: "first_name last_name" }] });
    }

    async getCustomerWithRoleById(_id) {
        return UserRoleSchema.findById(_id)
            .populate({ path: 'role_id', select: '_id name portal_type' })
    }

    async getTabletCustomerWithRoleById(_id) {
        return UserRoleSchema.findById(_id)
            .populate({ path: 'user_id', select: '_id first_name last_name email mobile_number country_code' })
            .populate({ path: 'sales_person_id', select: '-__v -created_by -updated_by' })
            .populate({ path: 'shipping_region_id', select: '-__v -created_by -updated_by' })
            .populate({ path: 'shipping_city_id', select: '-__v -created_by -updated_by' })
    }

    async deleteCustomer(_id) {
        return UserRoleSchema.updateOne(
            {
                _id
            },
            {
                is_deleted: true,
                external_id: "",
                unique_external_id: null,
            }
        );
    }

    async addCustomerDeviceAccess(body) {
        return UserRoleSchema.updateOne({ _id: body.userRoleId }, {
            $push: { device_access: { device_id: body.deviceId, type: body.deviceType, os: body.deviceOs } }
        })
    }

    async deleteCustomerDeviceAccess(body) {
        return UserRoleSchema.updateMany({ _id: body.userRoleId }, {
            $pull: { device_access: { device_id: body.deviceId } }
        })
    }

    async getCustomers(body) {
        const {
            tenantId = "",
            branchId = "",
            customerType = "",
            searchKey = "",
            status = "",
            salesPersonId = "",
            customerAppAccess = "",
            type = "",
            sortByName = false,
            sortType,
            supervisorId = "",
            onlyCustomerCount = false,
        } = body;

        const customerRoleId = await this.getCustomerRole(
            {
                _id: 1,
            },
            toLeanOption,
        );

        const match = {
            is_deleted: false,
            role_id: customerRoleId._id,
            tenant_id: tenantId
        }

        const otherProjection = {}
        const pipeline = [];

        const sort_by = {
            $sort: {}
        }

        if (sortType) {
            sort_by["$sort"]["created_at"] = sortType === "DESC" ? -1 : 1
        }

        if (branchId) {
            const salesPersonsOfBranch = await this.findSalesPersonsWithBranchId(
                branchId,
                null,
                { _id: 1 },
                toLeanOption,
            );

            match.sales_person_id = {
                $in: salesPersonsOfBranch.map(salesUserPro => salesUserPro._id)
            }
        }

        // PORTAL filters with New, Unassigned, App access filters
        switch (customerType) {
            case CUSTOMER_TYPE.NEW:
                const considerNew = await this.tenantAppSettingExist(
                    tenantId,
                    "consider_new_item",
                    toLeanOption,
                )

                match.created_at = {
                    $gte:
                        moment()
                            .add(considerNew
                                ? - considerNew.consider_new_item
                                : -30, "days"
                            )
                            .toDate()
                }

                pipeline.push({
                    $sort: { created_at: -1 }
                });
                break;

            case CUSTOMER_TYPE.INACTIVE:
                match.is_active = false
                break;

            case CUSTOMER_TYPE.ACTIVE:
                match.is_active = true
                break;

            case CUSTOMER_TYPE.UNASSIGNED:
                match.$or = [
                    { external_id: null },
                    { external_id: "" }
                ]
                break;

            case CUSTOMER_TYPE.APP_ACCESS:
                match.customer_app_access = true
                break;

            case CUSTOMER_TYPE.APP_REQUEST:
                pipeline.push({
                    $sort: {
                        customer_app_request: -1,
                        updated_at: -1,
                    }
                })
                break;
        }

        // SALES_APP ( for filtering customer of authenticated salesperson )
        if (salesPersonId) {
            match.sales_person_id = new mongoose.Types.ObjectId(salesPersonId)
        }

        // user_id of the user_role_id which is directly assigned to the salesperson's user_role_id as supervisor_id
        if (supervisorId) {
            let salesPersons = await UserRoleSchema.find(
                { supervisor_id: supervisorId, },
                { _id: 1 },
                toLeanOption
            );

            if (salesPersons.length) {
                salesPersons = salesPersons.map(s => s._id);
                match.sales_person_id = { $in: salesPersons }
            }
        }

        // PORTAL/MOBILE filters for active and in-active customers ( mobile app can send this too for fetching active customer )
        if (status) {
            match.is_active =
                status === ENTITY_STATUS.ACTIVE
                    ? true
                    : false
        }

        // PORTAL/MOBILE filter
        if (customerAppAccess) {
            match.customer_app_access =
                customerAppAccess === ENTITY_STATUS.ACTIVE
                    ? true
                    : false
        }

        // Adding $match stage first to the pipeline
        pipeline.unshift({ $match: match })

        // MOBILE filter, need to enable primary contact based search key
        if ((sortByName && type === LISTING_TYPES.ALL) || searchKey) {
            pipeline.push(
                {
                    $lookup: {
                        from: 'tenant_customers',
                        localField: 'user_id',
                        foreignField: '_id',
                        as: 'customers',
                    }
                },
                {
                    $addFields: {
                        customer: {
                            $first: "$customers"
                        }
                    }
                },
            )

            otherProjection["gps_coordinates"] = 1
        }
        // PORTAL/MOBILE filter
        if (searchKey) {
            pipeline.push(
                {
                    $addFields: {
                        mobileNumberStr: {
                            $toString: { $toLong: "$customer.mobile_number" }
                        }
                    }
                },
                {
                    $match: {
                        $or: [
                            { customer_id: new RegExp(searchKey, "i") },
                            { customer_name: new RegExp(searchKey, "i") },
                            { external_id: new RegExp(searchKey, "i") },
                            { customer_legal_name: new RegExp(searchKey, "i") },
                            { mobileNumberStr: new RegExp(searchKey) }
                        ]
                    }
                }
            )
        }

        if (sortType) {
            const sortStageIndex = pipeline.findIndex(ele => ele.$sort)

            if (sortStageIndex > -1) {
                pipeline[sortStageIndex]["$sort"] = {
                    ...sort_by.$sort,
                    ...pipeline[sortStageIndex]["$sort"],
                }
            }
            else {
                pipeline.push(sort_by)
            }
        }

        // To send only count of given filter
        if (onlyCustomerCount) {
            pipeline.push(
                {
                    $count: "customer_count"
                }
            )
        } else {
            pipeline.push(
                {
                    $lookup: {
                        from: 'user_roles',
                        localField: 'sales_person_id',
                        foreignField: '_id',
                        as: 'salesPersonRole',
                        pipeline: [
                            {
                                $project: {
                                    user_id: 1,
                                    branch_id: 1,
                                }
                            }
                        ]
                    }
                },
                {
                    $addFields: { salesPersonRole: { $first: "$salesPersonRole" } }
                },
                {
                    $lookup: {
                        from: 'users',
                        localField: 'salesPersonRole.user_id',
                        foreignField: '_id',
                        as: 'salesPerson',
                    }
                },
                {
                    $addFields: { salesPerson: { $first: "$salesPerson" } }
                },
                {
                    $project: {
                        _id: 1,
                        customer_id: 1,
                        customer_name: 1,
                        external_id: 1,
                        customer_legal_name: 1,
                        is_active: 1,
                        customer_app_access: 1,
                        customer_app_request: 1,
                        customer_catalog_mode: 1,
                        sales_person_id: 1,
                        last_mobile_login_time: 1,
                        last_tablet_login_time: 1,
                        salesPerson: {
                            first_name: "$salesPerson.first_name",
                            last_name: "$salesPerson.last_name",
                        },
                        branch_id: "$salesPersonRole.branch_id",
                        customer_name_insensitive: { "$toLower": "$customer_name" },
                        price_list_id: 1,
                        customer_first_name: 1,
                        customer_last_name: 1,
                        is_verified: 1,
                        customer_mobile_number: "$customer.mobile_number",
                        customer_country_code: "$customer.country_code",
                        is_payment_enabled: 1,
                        preferred_language: 1,
                        shipping_city_id: 1,
                        ...otherProjection,
                    }
                },
            );
        }

        if (
            sortByName &&
            type === LISTING_TYPES.ALL &&
            !onlyCustomerCount
        ) {
            pipeline.push({
                $sort: { customer_name_insensitive: 1 }
            })
        }

        switch (type) {
            case LISTING_TYPES.ALL:
                return await UserRoleSchema.aggregate(pipeline);
            default:
                let { perPage = 10, page = 1 } = body;
                perPage = parseInt(perPage);
                page = parseInt(page);
                const offset = perPage * (page - 1);
                pipeline.push(
                    {
                        $facet: {
                            list: [{ $skip: offset }, { $limit: perPage }],
                            count: [
                                {
                                    $count: 'count'
                                }
                            ]
                        }
                    },
                    {
                        $unwind: "$count"
                    }
                );

                const data = await UserRoleSchema.aggregate(pipeline);
                const list = data.length ? data[0].list : [];
                const count = data.length ? data[0].count.count : 0;
                return { list, count };
        }
    }

    async getCustomersForPayment(body) {
        const {
            tenantId,
            searchKey,
            salesPersonId,
            type,
            filters,
            perPage,
            page
        } = body;

        const customerRoleId = await this.getCustomerRole(
            {
                _id: 1
            },
            {
                lean: true
            }
        );

        const matchCondition = {
            is_deleted: false,
            is_active: true,
            role_id: customerRoleId._id,
            tenant_id: tenantId,
        };

        if (type !== CUSTOMER_PAYMENT_FILTER_TYPE.WITHOUT_PAGINATION) {
            //NOTE: This condition will required for payment module
            matchCondition.external_id = {
                $exists: true,
                $nin: [null, ""]
            }
        }

        switch (type) {
            case CUSTOMER_PAYMENT_FILTER_TYPE.PAYMENT_ENABLED: {
                matchCondition.is_payment_enabled = true;
                break;
            }

            case CUSTOMER_PAYMENT_FILTER_TYPE.PENDING: {
                matchCondition.is_payment_enabled = {
                    $ne: true
                }
                break;
            }
        }

        if (salesPersonId) {
            matchCondition.sales_person_id = new mongoose.Types.ObjectId(salesPersonId);
        }

        if (searchKey) {
            matchCondition.$or = [
                { external_id: new RegExp(searchKey, "i") },
                { customer_legal_name: new RegExp(searchKey, "i") },
            ]
        }

        let filterSortingPipeline = []

        if (filters) {
            matchCondition.external_id = {
                $in: filters
            }

            filterSortingPipeline = [
                {
                    $addFields: {
                        filterIndex: {
                            $indexOfArray: [
                                filters, "$external_id",
                            ]
                        }
                    }
                },
                {
                    $sort: {
                        filterIndex: 1
                    }
                }
            ]
        }

        const pipeline = [
            {
                $match: matchCondition
            },
            ...filterSortingPipeline,
            {
                $lookup: {
                    from: 'user_roles',
                    localField: 'sales_person_id',
                    foreignField: '_id',
                    as: 'salesPersonRole',
                    pipeline: [
                        {
                            $project: {
                                user_id: 1,
                                branch_id: 1,
                            }
                        }
                    ]
                }
            },
            {
                $addFields: {
                    salesPersonRole: { $first: "$salesPersonRole" },
                }
            },
            {
                $lookup: {
                    from: 'users',
                    localField: 'salesPersonRole.user_id',
                    foreignField: '_id',
                    as: 'salesPerson',
                }
            },
            {
                $addFields: {
                    salesPerson: { $first: "$salesPerson" },
                }
            },
            {
                $project: {
                    _id: 1,
                    external_id: 1,
                    customer_legal_name: 1,
                    customer_first_name: 1,
                    customer_last_name: 1,
                    customer_name: 1,
                    is_active: 1,
                    salesPerson: {
                        user_id: "$salesPerson._id",
                        user_role_id: "$salesPersonRole._id",
                        first_name: "$salesPerson.first_name",
                        last_name: "$salesPerson.last_name",
                    },
                    is_payment_enabled: 1,
                }
            }
        ];

        if (type !== CUSTOMER_PAYMENT_FILTER_TYPE.WITHOUT_PAGINATION) {
            const data = await UserRoleSchema.aggregate(
                [
                    ...pipeline,
                    {
                        $facet: {
                            list: [
                                { $skip: perPage * (page - 1) },
                                { $limit: perPage }
                            ],
                            count: [
                                { $count: 'count' }
                            ]
                        }
                    },
                    {
                        $unwind: "$count"
                    }
                ]
            );

            return {
                list: data[0]?.list ?? [],
                count: data[0]?.count?.count ?? 0,
            };
        }
        else {
            return await UserRoleSchema.aggregate(pipeline);
        }
    }

    async getCustomersForRewardProgram(body) {
        const {
            tenantId,
            searchKey,
            salesPersonId,
            type,
            filters,
            perPage,
            page
        } = body;

        const customerRoleId = await this.getCustomerRole(
            {
                _id: 1
            },
            {
                lean: true
            }
        );

        const matchCondition = {
            is_deleted: false,
            is_active: true,
            role_id: customerRoleId._id,
            tenant_id: tenantId,
            is_payment_enabled: true,
            external_id: {
                $exists: true,
                $nin: [null, ""]
            }
        };

        const filterRewardMembers = []

        if (type !== CUSTOMER_REWARD_PROGRAM_FILTER_TYPE.ALL) {
            filterRewardMembers.push({
                $lookup: {
                    from: "reward_program_members",
                    localField: "_id",
                    foreignField: "customer_user_role_id",
                    as: "rewardMemberInfo",
                    pipeline: [
                        {
                            $project: {
                                is_enrolled: 1,
                            }
                        }
                    ]
                }
            })

            switch (type) {
                case CUSTOMER_REWARD_PROGRAM_FILTER_TYPE.ENROLLED: {
                    filterRewardMembers.push({
                        $match: {
                            "rewardMemberInfo.is_enrolled": true
                        }
                    })
                    break;
                }

                case CUSTOMER_REWARD_PROGRAM_FILTER_TYPE.PENDING: {
                    filterRewardMembers.push({
                        $match: {
                            "rewardMemberInfo.is_enrolled": {
                                $ne: true
                            }
                        }
                    })
                    break;
                }
            }
        }

        if (salesPersonId) {
            matchCondition.sales_person_id = new mongoose.Types.ObjectId(salesPersonId);
        }

        if (searchKey) {
            matchCondition.$or = [
                { external_id: new RegExp(searchKey, "i") },
                { customer_legal_name: new RegExp(searchKey, "i") },
            ]
        }

        let filterSortingPipeline = []

        if (filters) {
            matchCondition.external_id = {
                $in: filters
            }
            filterSortingPipeline = [
                {
                    $addFields: {
                        filterIndex: {
                            $indexOfArray: [
                                filters, "$external_id",
                            ]
                        }
                    }
                },
                {
                    $sort: {
                        filterIndex: 1
                    }
                }
            ]
        }

        const customerProjection = {
            _id: 1,
            external_id: 1,
            customer_legal_name: 1,
            customer_first_name: 1,
            customer_last_name: 1,
            customer_name: 1,
            is_active: 1,
            customer_payment_term_info: 1,
        }

        const pipeline = [
            {
                $match: matchCondition
            },
            {
                $project: {
                    ...customerProjection,
                    sales_person_id: 1,
                }
            },
            ...filterSortingPipeline,
            ...filterRewardMembers,
            {
                $lookup: {
                    from: 'user_roles',
                    localField: 'sales_person_id',
                    foreignField: '_id',
                    as: 'salesPersonRole',
                    pipeline: [
                        {
                            $project: {
                                user_id: 1,
                            }
                        }
                    ]
                }
            },
            {
                $addFields: {
                    salesPersonRole: { $first: "$salesPersonRole" },
                }
            },
            {
                $lookup: {
                    from: 'users',
                    localField: 'salesPersonRole.user_id',
                    foreignField: '_id',
                    as: 'salesPerson',
                }
            },
            {
                $addFields: {
                    salesPerson: { $first: "$salesPerson" },
                }
            },
            {
                $project: {
                    ...customerProjection,
                    sales_person: {
                        user_id: "$salesPerson._id",
                        user_role_id: "$salesPersonRole._id",
                        first_name: "$salesPerson.first_name",
                        last_name: "$salesPerson.last_name",
                    },
                }
            },
            {
                $facet: {
                    list: [
                        { $skip: perPage * (page - 1) },
                        { $limit: perPage }
                    ],
                    count: [
                        { $count: 'count' }
                    ]
                }
            },
            {
                $unwind: "$count"
            }
        ];

        const data = await UserRoleSchema.aggregate(pipeline);
        return {
            list: data[0]?.list ?? [],
            count: data[0]?.count?.count ?? 0,
        };
    }

    async customerForExport(tenantId) {
        const customerRole = await this.getCustomerRole();
        return await UserRoleSchema.aggregate([
            {
                '$match': {
                    'tenant_id': tenantId,
                    'is_deleted': false,
                    // 'is_active': true,
                    'role_id': customerRole._id,
                    'collection_name': 'tenant_customers'
                }
            }, {
                '$lookup': {
                    'from': 'tenant_customers',
                    'localField': 'user_id',
                    'foreignField': '_id',
                    'as': 'user_id'
                }
            }, {
                '$lookup': {
                    'from': 'countries',
                    'localField': 'shipping_country_id',
                    'foreignField': '_id',
                    'as': 'shipping_country_id'
                }
            }, {
                '$lookup': {
                    'from': 'regions',
                    'localField': 'shipping_region_id',
                    'foreignField': '_id',
                    'as': 'shipping_region_id'
                }
            }, {
                '$lookup': {
                    'from': 'cities',
                    'localField': 'shipping_city_id',
                    'foreignField': '_id',
                    'as': 'shipping_city_id'
                }
            }, {
                '$addFields': {
                    'user_id': {
                        '$first': '$user_id'
                    },
                    'shipping_country_id': {
                        '$first': '$shipping_country_id'
                    },
                    'shipping_region_id': {
                        '$first': '$shipping_region_id'
                    },
                    'shipping_city_id': {
                        '$first': '$shipping_city_id'
                    }
                }
            }, {
                '$project': {
                    'customer_id': 1,
                    'external_id': 1,
                    'customer_name': 1,
                    'customer_legal_name': 1,
                    'sales_person_id': 1,
                    'price_list_id': 1,
                    'customer_first_name': 1,
                    'customer_last_name': 1,
                    'customer_mobile_number': '$user_id.mobile_number',
                    'customer_email': 1,
                    'customer_app_access': 1,
                    'customer_catalog_mode': 1,
                    'preferred_language': 1,
                    'shipping_address': 1,
                    'shipping_country': '$shipping_country_id.name',
                    'shipping_region': '$shipping_region_id.name',
                    'shipping_city': '$shipping_city_id.name',
                    'country_secondary_lan_code': '$shipping_country_id.secondary_language_code',
                    'country_secondary_lan': '$shipping_country_id.secondary_language_name',
                    'gps_coordinates_latitude': '$gps_coordinates.latitude',
                    'gps_coordinates_longitude': '$gps_coordinates.longitude',
                    'shipping_mobile_number': 1,
                    'is_active': 1,
                }
            }
        ])
    }

    async updateCustomerStatus(body, headers) {
        const { status, statusType } = body;
        const query = {
            _id: { $in: body.customers }
        }

        const update = {
            updated_by: headers.userDetails._id
        };
        const assignStatus = status === ENTITY_STATUS.ACTIVE ? true : false
        switch (statusType) {
            case CUSTOMER_STATUS_TYPE.CUSTOMER_APP_ACCESS:
                update["customer_app_access"] = assignStatus
                break;

            case CUSTOMER_STATUS_TYPE.STATUS:
                update["is_active"] = assignStatus;
                break;
            case CUSTOMER_STATUS_TYPE.APP_ACCESS:
                update["customer_app_access"] = assignStatus;
                update["customer_catalog_mode"] = assignStatus;
                update["customer_app_request"] = false;
                break;
            case CUSTOMER_STATUS_TYPE.ONLY_APP_ACCESS:
                update["customer_app_access"] = assignStatus;
                if (update["customer_app_access"]) {
                    update["customer_app_request"] = false;
                }
                break;
            case CUSTOMER_STATUS_TYPE.ONLY_CATALOG_ACCESS:
                update["customer_catalog_mode"] = assignStatus;
                break;
        }

        return UserRoleSchema.updateMany(query, update);
    }

    async getTenantInfo(_id) {
        return TenantSchema.findById(_id, 'name legal_name street_name region city primary_contact_id')
            .populate({ path: "primary_contact_id", select: "country_code mobile_number" })
            .populate({ path: "region", select: "name" })
            .populate({ path: "city", select: "name" })
    }

    async addShippingLabel(body, header) {
        const {
            tenant_id,
            tenant_legal_name,
            branches,
            shipping_label_logo,
            is_active,
        } = body

        return ShippingLabelSchema({
            tenant_id,
            tenant_legal_name,
            branches,
            shipping_label_logo,
            is_active,
            created_by: header.userDetails._id,
            updated_by: header.userDetails._id
        })
    }

    async shippingLabelExist(tenantId, projection = "", options = {}) {
        return ShippingLabelSchema.findOne({ tenant_id: tenantId }, projection, options)
    }

    async getShippingLabelLogoUrl(tenantId) {
        const shippingLabelInfo = await this.shippingLabelExist(
            tenantId,
            "-_id shipping_label_logo",
            {
                lean: true,
            }
        )

        if (!shippingLabelInfo.shipping_label_logo)
            return

        return VALUES.awsPublicBucketBaseURL +
            "shipping-label" +
            `/${tenantId}/` +
            shippingLabelInfo.shipping_label_logo
    }

    async tenantAppSettingExist(tenantId, projection, options) {
        return TenantAppSettingSchema.findOne({ tenant_id: tenantId }, projection, options)
    }

    async tenantsAppSettingExist(filter, projection, options) {
        return TenantAppSettingSchema.find(filter, projection, options)
    }

    async getAppSettingDecimalPoint(tenantId) {
        const tenantAppSettings = await this.tenantAppSettingExist(
            tenantId,
            "-_id decimal_points",
            {
                lean: true,
            }
        )

        return tenantAppSettings?.decimal_points ?? 0
    }

    async updateTenantOrderPrefix(body, headers) {
        const prefix = `${body.tenantId}_${body.prefix || ""}`;
        let tenantOrderPrefix = await this.findTenantOrderPrefix({ _id: prefix });
        if (!tenantOrderPrefix) {
            tenantOrderPrefix = await this.createTenantPrefix();
            tenantOrderPrefix._id = prefix;
            tenantOrderPrefix.tenant_id = body.tenantId;
            tenantOrderPrefix.prefix = body.prefix;
            tenantOrderPrefix.created_by = headers.userDetails._id;
            tenantOrderPrefix.updated_by = headers.userDetails._id;
        }

        if (!tenantOrderPrefix.is_current) {
            //TODO: update other prefixes as not current
            await this.updateTenantOrderPrefixes({ _id: { $ne: tenantOrderPrefix._id }, tenant_id: body.tenantId, is_current: true }, { is_current: false });
        }
        tenantOrderPrefix.is_current = true;
        tenantOrderPrefix.updated_by = headers.userDetails._id;
        await tenantOrderPrefix.save();
    }

    async findTenantOrderPrefix(filter = {}, projection = {}, options = {}) {
        return TenantOrderPrefixSchema.findOne(filter, projection, options);
    }

    async updateTenantOrderPrefixes(filter = {}, updateObject = {}, options = {}) {
        if (isEmpty(filter)) {
            throw Error("filter should not be empty")
        }
        return TenantOrderPrefixSchema.updateMany(filter, updateObject, options);
    }

    /**
     *
     * @param {TenantOrderPrefixSchema} prefixObjet, need to maintain _id manually
     * @returns
     */
    async createTenantPrefix(prefixObjet = {}) {
        return new TenantOrderPrefixSchema(prefixObjet);
    }

    async configurationAppSetting(tenant_id, header) {

        return TenantAppSettingSchema({
            tenant_id: tenant_id,
            quantity_label: 5,
            consider_new_item: 30,
            price_change: true,
            hide_out_of_stock_product: false,
            reduce_inventory: false,
            customer_app_access: true,
            catalog_mode: false,
            preferred_language: "en",
            created_by: header.userDetails._id,
            updated_by: header.userDetails._id
        })
    }

    async allowNewValidation(tenantId, type) { // Dec 30 users and customer limit validation
        if (type === TENANT_ALLOW_VALIDATION.CUSTOMER) {
            return UserRoleSchema.countDocuments({ collection_name: "tenant_customers", tenant_id: tenantId, is_deleted: false, is_active: true })
        } else if (type === TENANT_ALLOW_VALIDATION.USER) {
            return UserRoleSchema.countDocuments({ collection_name: "users", tenant_id: tenantId, is_deleted: false, is_active: true })
        }
    } // Dec 30 users and customer limit validation

    async tenantDetail(_id) {
        return TenantSchema.findById(_id, 'name legal_name street_name country region city primary_contact_id')
            // .populate({ path: "primary_contact_id", select: "country_code mobile_number" })
            // .populate({ path: "region", select: "name" })
            // .populate({ path: "city", select: "name" })
            .populate({ path: "country" })
    }

    async tenantSalesPersons(tenantId, status = ENTITY_STATUS.ACTIVE) {
        const salesPersonRole = await this.getSalesPersonRole("_id")

        const filter = {
            is_deleted: false,
            tenant_id: tenantId,
            role_id: salesPersonRole?._id || "",
        }

        if (status !== LISTING_TYPES.ALL) {
            filter["is_active"] = status === ENTITY_STATUS.ACTIVE ? true : false
        }

        return UserRoleSchema
            .find(filter)
            .populate({ path: "user_id", select: "first_name last_name country_code mobile_number email" })
    }

    async tenantMasterPriceList(req, status = ENTITY_STATUS.ACTIVE) {
        const url = VALUES.internalServiceBaseURL + "tenant/masterPriceList?tenantId=" + req.body.tenantId + "&status=" + status
        const authResponse = await httpService(req).get(url)
        const details = authResponse?.["data"]?.["data"]

        return details
    }

    async columnField(filter = {}, projection = {}, options = {}) {
        return ColumnFieldSchema.findOne(filter, projection, options)
    }

    async tempExcelFile(fileS3Url, newFileName) {
        return new Promise((resolve, reject) => {
            const sheetsPath = path.join(__dirname, "../Assets/Datasheets/" + newFileName);

            https.get(fileS3Url, response => {
                const data = new Stream();

                response.on('data', (chunk) => {
                    data.push(chunk);
                })

                response.on('end', () => {
                    fs.writeFile(sheetsPath, data.read(), function (err, result) {
                        if (err) {
                            reject(err)
                            return;
                        }
                        resolve(result);
                    });
                })

            }).end()

        })
    }

    async customerExcel(req, tenantId, salesPersons, tenantPriceList, customerColumnList) {
        // const sheetsPath = `${__dirname}` + '/../Assets/Datasheets'
        const sheetsPath = path.join(__dirname, "/../Assets/Datasheets/")

        const tenantDetail = await this.tenantDetail(tenantId)
        const regions = await CommonModal.regionListByCountry(tenantDetail.country._id);
        let i;
        i = 65;
        var firstArr = [""];
        var secondArr = [];
        while (i <= 90) {
            firstArr.push(String.fromCharCode(i))
            secondArr.push(String.fromCharCode(i))
            i++;
        }

        var thirdArr = []

        for (let index = 0; index < firstArr.length; index++) {
            const element = firstArr[index];

            for (let index = 0; index < secondArr.length; index++) {
                const secondElement = secondArr[index];
                const key = `${element}` + `${secondElement}`
                thirdArr.push(key)
            }
        }

        let workbook = new excelJS.Workbook();  // Create a new workbook

        await this.tempExcelFile(VALUES.CUSTOMER_SAMPLE_EXCEL_FILE + '/Customer+Sheet.xlsx', 'customerSample.xlsx')
        workbook = await workbook.xlsx.readFile(`${sheetsPath}/customerSample.xlsx`)

        const worksheet = workbook.getWorksheet("customer");
        worksheet.properties.defaultRowHeight = 15;
        worksheet.properties.defaultColWidth = 30;

        for (let index = 0; index < customerColumnList.length; index++) {
            if (customerColumnList[index] === "Customer Name") {
            } else if (customerColumnList[index] === "Legal Name") {
                // for (let index1 = 2; index1 < 1001; index1++) {
                //     worksheet.getCell(thirdArr[index] + index1).dataValidation = {
                //         allowBlank: false,
                //         showErrorMessage: true,
                //         errorStyle: 'error',
                //         error: 'The value Valid',
                //     };
                // }
            } else if (customerColumnList[index] === "Sales Person") {
                for (let index1 = 2; index1 < 1001; index1++) {
                    worksheet.getCell(thirdArr[index] + index1).dataValidation = {
                        type: 'list',
                        allowBlank: false,
                        formulae: ['values!$A$2:$A$1000']
                    };
                }
            } else if (customerColumnList[index] === "Price List") {
                for (let index1 = 2; index1 < 1001; index1++) {
                    worksheet.getCell(thirdArr[index] + index1).dataValidation = {
                        type: 'list',
                        allowBlank: false,
                        formulae: ['values!$B$2:$B$1000']
                    };
                }
            } else if (customerColumnList[index] === "Customer App Access") {
                for (let index1 = 2; index1 < 1001; index1++) {
                    worksheet.getCell(thirdArr[index] + index1).dataValidation = {
                        type: 'list',
                        allowBlank: true,
                        formulae: ['"TRUE, FALSE"']
                    };
                }
            } else if (customerColumnList[index] === "Catalog Mode") {
                for (let index1 = 2; index1 < 1001; index1++) {
                    worksheet.getCell(thirdArr[index] + index1).dataValidation = {
                        type: 'list',
                        allowBlank: true,
                        formulae: ['"TRUE, FALSE"']
                    };
                }
            } else if (customerColumnList[index] === "Preferred Language") {
                for (let index1 = 2; index1 < 1000; index1++) {
                    worksheet.getCell(thirdArr[index] + index1).dataValidation = {
                        type: 'list',
                        allowBlank: true,
                        formulae: ['values!$C$2:$C$1000']
                    };
                }
            }
            // else if (customerColumnList[index] === "Shipping Country") {
            //     for (let index1 = 2; index1 < 1001; index1++) {
            //         worksheet.getCell(thirdArr[index] + index1).dataValidation = {
            //             type: 'list',
            //             allowBlank: false,
            //             formulae: ['values!$D$2:$D$1000']
            //         };
            //     }
            // }
            else if (customerColumnList[index] === "Region") {
                for (let index1 = 2; index1 < 1001; index1++) {
                    worksheet.getCell(thirdArr[index] + index1).dataValidation = {
                        type: 'list',
                        allowBlank: false,
                        formulae: [`regions!$A$1:$${thirdArr[regions.length - 1]}$1`]
                    };
                }
            }
            else if (customerColumnList[index] === "Active") {
                for (let index1 = 2; index1 < 1001; index1++) {
                    worksheet.getCell(thirdArr[index] + index1).dataValidation = {
                        type: 'list',
                        allowBlank: true,
                        formulae: ['"TRUE, FALSE"']
                    };
                }
            }

            worksheet.getCell(thirdArr[index] + 1).font = {
                name: 'Arial',
                size: 10,
                bold: true,
            };
            worksheet.getCell(thirdArr[index] + 1).value = customerColumnList[index];
        }

        if (req.body.dataType === DATA_SHEET.DATA_TYPE.CUSTOMER && req.body.operationType === DATA_SHEET.OPERATION_TYPE.CREATE) {
            // await this.tempExcelFile(VALUES.CUSTOMER_SAMPLE_EXCEL_FILE+'/Customer+Sheet.xlsx','customerSample.xlsx')
            // workbook = await workbook.xlsx.readFile(`${sheetsPath}/customerSample.xlsx`)

            // const worksheet = workbook.addWorksheet("customer"); // New Worksheet
            // worksheet.properties.defaultRowHeight = 15;
            // worksheet.properties.defaultColWidth = 30;


        } else if (req.body.dataType === DATA_SHEET.DATA_TYPE.CUSTOMER && req.body.operationType === DATA_SHEET.OPERATION_TYPE.UPDATE) {
            // await this.tempExcelFile(VALUES.CUSTOMER_SAMPLE_EXCEL_FILE+'/customerUpdate.xlsx','customerSample.xlsx')
            // workbook = await workbook.xlsx.readFile(`${sheetsPath}/customerSample.xlsx`)
            // const worksheet = workbook.getWorksheet("customer"); // New Worksheet
            // const worksheet = workbook.addWorksheet("customer"); // New Worksheet
            // worksheet.properties.defaultRowHeight = 15;
            // worksheet.properties.defaultColWidth = 30;

            const allSalesPersons = await this.tenantSalesPersons(tenantId, LISTING_TYPES.ALL)
            const allPriceList = await this.tenantMasterPriceList(req, LISTING_TYPES.ALL)

            const arraySalesPersons = []
            for (let index = 0; index < allSalesPersons.length; index++) {
                let salesPerson = {}
                salesPerson['salesPersonId'] = allSalesPersons[index]._id
                salesPerson['name'] = allSalesPersons[index].user_id.first_name + " " + allSalesPersons[index].user_id.last_name
                salesPerson['mobileNumber'] = allSalesPersons[index].user_id.mobile_number
                salesPerson['email'] = allSalesPersons[index].user_id.email
                salesPerson['needToAppendMobileNumber'] = false

                arraySalesPersons.push(salesPerson)
            }

            for (let index = 0; index < arraySalesPersons.length; index++) {
                const newArray = arraySalesPersons.filter(item =>
                    item.name === arraySalesPersons[index].name && (
                        item.email != arraySalesPersons[index].email ||
                        item.mobileNumber != arraySalesPersons[index].mobileNumber
                    )
                )

                if (newArray.length > 0) {
                    arraySalesPersons[index].needToAppendMobileNumber = true
                }

                if (arraySalesPersons[index].needToAppendMobileNumber) {
                    arraySalesPersons[index]["salespersonName"] =
                        arraySalesPersons[index].name + " " + `(${arraySalesPersons[index].mobileNumber})`
                }
                else {
                    arraySalesPersons[index]["salespersonName"] = arraySalesPersons[index].name
                }
            }

            const customers = await this.customerForExport(tenantId)

            for (let index = 0; index < customers.length; index++) {
                const alphabet = 0
                const assignSalesPerson = arraySalesPersons.find((item) => { return (item.salesPersonId.toString() == customers[index].sales_person_id.toString()) })

                if (assignSalesPerson) {
                    customers[index].sales_person_id = assignSalesPerson.salespersonName
                } else {
                    customers[index].sales_person_id = ""
                }

                const assignPrice = allPriceList.find((item) => { return (item._id.toString() == customers[index].price_list_id.toString()) })
                if (assignPrice) {
                    customers[index].price_list_id = assignPrice.price_name
                } else {
                    customers[index].price_list_id = ""
                }

                if (customers[index].preferred_language.toString() === customers[index].country_secondary_lan_code?.toString()) {
                    customers[index].preferred_language = customers[index].country_secondary_lan
                } else if (customers[index].preferred_language.toString() === "en") {
                    customers[index].preferred_language = "English"
                } else {
                    customers[index].preferred_language = ""
                }
                customers[index].catalog_mode = customers[index].customer_catalog_mode || false

                const fields = [
                    "customer_id",
                    "external_id",
                    "customer_name",
                    "customer_legal_name",
                    "sales_person_id",
                    "price_list_id",
                    "customer_first_name",
                    "customer_last_name",
                    "customer_mobile_number",
                    "customer_email",
                    "customer_app_access",
                    "catalog_mode",
                    "preferred_language",
                    "shipping_mobile_number",
                    // "shipping_country",
                    "gps_coordinates_latitude",
                    "gps_coordinates_longitude",
                    "shipping_address",
                    "shipping_region",
                    "shipping_city",
                    "is_active"
                ]

                for (let i = 0; i < fields.length; i++) {
                    worksheet.getCell(thirdArr[alphabet + i] + (index + 2)).value = (customers[index][fields[i]]);
                }
            }

        } else if (req.body.dataType === DATA_SHEET.DATA_TYPE.CUSTOMER && req.body.operationType === DATA_SHEET.OPERATION_TYPE.CUSTOM_FIELDS) {
            await this.tempExcelFile(VALUES.CUSTOMER_SAMPLE_EXCEL_FILE + '/customerCustomField.xlsx', 'customerSample.xlsx')
            workbook = await workbook.xlsx.readFile(`${sheetsPath}/customerSample.xlsx`)

            if (customerColumnList) {
                for (let index = 0; index < customerColumnList.length; index++) {
                    worksheet.getCell(thirdArr[index] + 8).font = {
                        name: 'Arial',
                        size: 10,
                        bold: true,
                    };
                    worksheet.getCell(thirdArr[index] + 8).value = customerColumnList[index];
                }
            }
        }

        const secondWorksheet = workbook.getWorksheet("values");
        // const secondWorksheet = workbook.addWorksheet("values"); // second Worksheet
        secondWorksheet.properties.defaultRowHeight = 15;
        secondWorksheet.properties.defaultColWidth = 20;

        secondWorksheet.getCell('A1').font = {
            name: 'Arial',
            size: 10,
            bold: true,
        };
        secondWorksheet.getCell('A1').value = "Sales Person";
        secondWorksheet.getCell('B1').font = {
            name: 'Arial',
            size: 10,
            bold: true,
        };
        secondWorksheet.getCell('B1').value = "Pricelist";
        secondWorksheet.getCell('C1').font = {
            name: 'Arial',
            size: 10,
            bold: true,
        };
        secondWorksheet.getCell('C1').value = "Preffered Language";
        secondWorksheet.getCell('D1').font = {
            name: 'Arial',
            size: 10,
            bold: true,
        };
        secondWorksheet.getCell('D1').value = "Country";

        salesPersons.forEach((salesPersons, index) => {
            secondWorksheet.getCell("A" + (index + 2)).value = salesPersons.salespersonName;
        });
        tenantPriceList.forEach((priceList, index) => {
            secondWorksheet.getCell("B" + (index + 2)).value = priceList.price_name;
        })
        secondWorksheet.getCell("C2").value = "English";
        secondWorksheet.getCell("C3").value = tenantDetail.country.secondary_language_name;
        secondWorksheet.getCell("D2").value = tenantDetail.country.name

        // const thirdWorksheet = workbook.addWorksheet("regions"); // third Worksheet
        // const thirdWorksheet = workbook.getWorksheet("regions");
        // thirdWorksheet.properties.defaultRowHeight = 15;
        // thirdWorksheet.properties.defaultColWidth = 20;

        // for (let index = 0; index < regions.length; index++) {
        //     const regionIndex = index;
        //     const regionName = regions[index].name;
        //     const regionId = regions[index]._id;
        //     const cities = await CommonModal.cityListByRegion(tenantDetail.country._id, regionId);

        //     for (let index = regionIndex; index < regions.length; index++) {
        //         const element = thirdArr[index];
        //         thirdWorksheet.getCell(element + 1).font = {
        //             name: 'Arial',
        //             size: 10,
        //             bold: true,
        //         };
        //         thirdWorksheet.getCell(element + 1).value = regionName;
        //     }
        //     for (let index = 0; index < cities.length; index++) {
        //         const element = thirdArr[regionIndex];
        //         const cityName = cities[index].name;
        //         thirdWorksheet.getCell(element + (index + 2)).value = cityName;
        //     }

        // }

        // const sheetsPath = `${__dirname}` + '/../Assets/Datasheets'
        await workbook.xlsx.writeFile(`${sheetsPath}/customer.xlsx`)

        const Path = path.join(__dirname, "/../Assets/Datasheets/customerSample.xlsx");
        fs.unlinkSync(Path);
        const fileUpload = new FileUpload(BUCKET_TYPE.PUBLIC);
        if (req.body.dataType === DATA_SHEET.DATA_TYPE.CUSTOMER && req.body.operationType === DATA_SHEET.OPERATION_TYPE.CREATE) {
            await fileUpload.uploadFiles(FILE_PATH.DATA_SHEET_CUSTOMER_EXPORT_SHEET + `/${tenantId}/customer/create/export`, 'customer.xlsx', `${sheetsPath}/customer.xlsx`);
            const url = process.env.AWS_PUBLIC_BUCKET_BASE_URL + `dataSheet/${tenantId}/customer/create/export/customer.xlsx`
            return url;
        } else if (req.body.dataType === DATA_SHEET.DATA_TYPE.CUSTOMER && req.body.operationType === DATA_SHEET.OPERATION_TYPE.UPDATE) {
            await fileUpload.uploadFiles(FILE_PATH.DATA_SHEET_CUSTOMER_EXPORT_SHEET + `/${tenantId}/customer/update/export`, 'customer.xlsx', `${sheetsPath}/customer.xlsx`);
            const url = process.env.AWS_PUBLIC_BUCKET_BASE_URL + `dataSheet/${tenantId}/customer/update/export/customer.xlsx`
            return url;
        } else if (req.body.dataType === DATA_SHEET.DATA_TYPE.CUSTOMER && req.body.operationType === DATA_SHEET.OPERATION_TYPE.CUSTOM_FIELDS) {
            await fileUpload.uploadFiles(FILE_PATH.DATA_SHEET_CUSTOMER_EXPORT_SHEET + `/${tenantId}/customer/customField/export`, 'customer.xlsx', `${sheetsPath}/customer.xlsx`);
            const url = process.env.AWS_PUBLIC_BUCKET_BASE_URL + `dataSheet/${tenantId}/customer/customField/export/customer.xlsx`
            return url;
        }

    }

    async getTenantById(_id, projection = {}, options = {}) {
        return TenantSchema.findById(_id, projection, options);
    }

    async updateExcelFile(req) {
        const {
            fileName,
            dataType,
            operationType,
            updateType,
            apiVersion,
        } = req.body;
        const tenantId = Number(req.body.tenantId);

        if ([DATA_SHEET.DATA_TYPE.PRODUCT, DATA_SHEET.DATA_TYPE.INVENTORY, DATA_SHEET.DATA_TYPE.PRICE].includes(dataType)) {
            const payload = {
                tenantId,
                fileName,
                dataType,
                operationType,
                updateType,
                type: DATA_SHEET.TYPE.IMPORT,
                apiVersion,
            }
            const isUpdatedFile = await Api.put(req, "dataSheet", payload)
            return isUpdatedFile
        }

        const s3FilePath = process.env.AWS_PUBLIC_BUCKET_BASE_URL + `dataSheet/${tenantId}/${dataType.toLowerCase()}/${operationType.toLowerCase()}/import/${fileName}`
        const sheetsPath = path.join(__dirname, "/../Assets/Datasheets/")
        let i;
        i = 65;
        var firstArr = [""];
        var secondArr = [];
        while (i <= 90) {
            firstArr.push(String.fromCharCode(i))
            secondArr.push(String.fromCharCode(i))
            i++;
        }

        var thirdArr = []

        for (let index = 0; index < firstArr.length; index++) {
            const element = firstArr[index];

            for (let index = 0; index < secondArr.length; index++) {
                const secondElement = secondArr[index];
                const key = `${element}` + `${secondElement}`
                thirdArr.push(key)
            }
        }

        let workbook = new excelJS.Workbook();

        await this.tempExcelFile(s3FilePath, `${fileName}`)
        workbook = await workbook.xlsx.readFile(`${sheetsPath}/${fileName}`)

        let worksheet;
        if (dataType === DATA_SHEET.DATA_TYPE.CUSTOMER) {
            worksheet = workbook.getWorksheet("customer");
            if (!worksheet) {
                return false;
            }
        }

        worksheet.getCell(thirdArr[worksheet.actualColumnCount] + 1).font = {
            name: 'Arial',
            size: 10,
            bold: true,
        };
        worksheet.getCell(thirdArr[worksheet.actualColumnCount] + 1).value = "Status";

        for (let i = 2; i <= worksheet.actualRowCount; i++) {
            worksheet.getCell(thirdArr[worksheet.actualColumnCount - 1] + i).value = DATA_SHEET.SHEET_STATUS.PENDING;
        }

        worksheet.getCell(thirdArr[worksheet.actualColumnCount] + 1).font = {
            name: 'Arial',
            size: 10,
            bold: true,
        };
        worksheet.getCell(thirdArr[worksheet.actualColumnCount] + 1).value = "Validations";

        let excelTitles = [];

        if (dataType === DATA_SHEET.DATA_TYPE.CUSTOMER) {

            const fields = {
                "External id": "external_id",
                "Customer Name": "customer_name",
                "Legal Name": "customer_legal_name",
                "Sales Person": "sales_person",
                "Price List": "price_list",
                "First Name": "customer_first_name",
                "Last Name": "customer_last_name",
                "Mobile Number": "customer_mobile_number",
                "Email": "customer_email",
                "Customer App Access": "customer_app_access",
                "Catalog Mode": "catalog_mode",
                "Preferred Language": "preferred_language",
                "Shipping Mobile Number": "shipping_mobile_number",
                "Region": "shipping_region",
                "City": "shipping_city",
                "Street Address": "shipping_address",
                // "shipping_country",
                "GPS Coordinate Lat": "gps_coordinates_latitude",
                "GPS Coordinate Long": "gps_coordinates_longitude",
                "Active": "is_active",
                "Status": "status",
                "Validations": "validations"
            }

            if (
                [
                    DATA_SHEET.OPERATION_TYPE.UPDATE,
                    DATA_SHEET.OPERATION_TYPE.CUSTOM_FIELDS
                ].includes(operationType)
            ) {
                fields["Customer Id"] = "customer_id"
            }
            let excelData = [];

            // excel to json converter (only the first sheet)
            worksheet.eachRow((row, rowNumber) => {
                // rowNumber 0 is empty
                if (rowNumber > 0) {
                    // get values from row
                    let rowValues = row.values;
                    // remove first element (extra without reason)
                    rowValues.shift();
                    // titles row
                    if (rowNumber === 1) excelTitles = rowValues;
                    // table data
                    else {
                        // create object with the titles and the row values (if any)
                        let rowObject = {}

                        for (let i = 0; i < excelTitles.length; i++) {
                            const title = fields[excelTitles[i]];
                            const value = rowValues[i] ?? '';

                            rowObject[title] = value;
                        }
                        excelData.push(rowObject);
                    }
                }
            })

            const [tenantInfo, salesPersons, activeCustomers, tenantAppSetting] = await Promise.all([
                this.getTenantById(tenantId),
                this.tenantSalesPersons(tenantId),
                this.allowNewValidation(tenantId, TENANT_ALLOW_VALIDATION.CUSTOMER),
                this.tenantAppSettingExist(tenantId)
            ]);

            const tenantMasterPriceList = await this.tenantMasterPriceList(req)
            const customerRole = await RoleModal.getRoleByFilter({ name: "Customer", portal_type: VALUES.portals.CUSTOMER_APP, is_active: true, is_deleted: false }, { _id: 1 });
            const country = await CommonModal.findCountryById(tenantInfo.country)

            for (let index = 0; index < excelData.length; index++) {
                if (excelData[index].status === DATA_SHEET.SHEET_STATUS.PENDING) {
                    const restData = {
                        tenantInfo,
                        salesPersons,
                        activeCustomers,
                        tenantAppSetting,
                        tenantMasterPriceList,
                        customerRole,
                        country,
                        operationType,
                        fields,
                        excelTitles,
                    }

                    const Model = new CustomerDataSheetModel(this)

                    const validation = await Model.checkValidation(
                        req,
                        excelData[index],
                        restData,
                    )

                    if (validation.length) {
                        const stringValidation = [...new Set(validation)].toString();
                        worksheet.getCell(thirdArr[worksheet.actualColumnCount - 1] + (index + 2)).value = stringValidation;
                    }
                    else {
                        worksheet.getCell(thirdArr[worksheet.actualColumnCount - 1] + (index + 2)).value = "Passed Validation";
                    }
                }
            }
        }
        await workbook.xlsx.writeFile(`${sheetsPath}/${fileName}`)

        const fileUpload = new FileUpload(BUCKET_TYPE.PUBLIC);
        await fileUpload.uploadFiles(FILE_PATH.DATA_SHEET_CUSTOMER_EXPORT_SHEET + `/${tenantId}/${dataType.toLowerCase()}/${operationType.toLowerCase()}/import`, fileName, `${sheetsPath}/${fileName}`);

        return true;
    }

    async createExcelFileRecord(body, header) {
        return TenantDataSheetSchema({
            tenant_id: body.tenantId,
            file_name: body.fileName,
            data_type: body.dataType,
            operation_type: body.operationType,
            update_type: body.updateType || undefined,
            original_file_name: body.originalFileName,
            status: DATA_SHEET.STATUS.FOR_REVIEW,
            is_deleted: false,
            created_by: header.userDetails._id,
            updated_by: header.userDetails._id
        })
    }

    async getFile(fileId) {
        return TenantDataSheetSchema.findById(fileId)
    }

    async getFileByName(fileName) {
        return TenantDataSheetSchema.findOne({ file_name: fileName })
    }

    async getDataSheetList(body, headers) {
        let { tenantId, searchKey = "", page = 0, perPage = 10 } = body;
        const offset = (page - 1) * perPage;

        const pipeline = [];
        const $match = {
            tenant_id: tenantId,
            is_deleted: false
        };
        if (searchKey) {
            $match["original_file_name"] = new RegExp(searchKey, "i")
        }
        pipeline.push(
            {
                $match
            },
            {
                $sort: { created_at: -1 }
            },
            {
                $facet: {
                    list: [{ $skip: offset }, { $limit: perPage }],
                    count: [{ $count: "count" }]
                }
            },
            {
                $unwind: "$count"
            }
        );
        const data = await TenantDataSheetSchema.aggregate(pipeline);
        const list = data.length ? data[0].list : [];
        const count = data.length ? data[0].count.count : 0;
        return { list, count };
    }

    async findSheets(body) {
        let { fileIds, tenantId } = body;

        const data = await TenantDataSheetSchema.find({
            _id: { $in: fileIds },
            tenant_id: tenantId,
            is_deleted: false,
        });
        return data;
    }



    async updateBulkCustomers(filter, updateFields, headers, options) {
        return UserRoleSchema.updateMany(
            filter,
            {
                "$set": !headers.userDetails._id
                    ? updateFields
                    : {
                        ...updateFields,
                        "updated_by": headers.userDetails._id,
                    },
            },
            options
        )
    }

    async updateBulkDataSheetFiles(filter, updateFields, headers) {
        return TenantDataSheetSchema.updateMany(
            filter,
            {
                "$set": {
                    ...updateFields,
                    "updated_by": headers.userDetails._id,
                },
            }
        )
    }

    async tenantInfo(filter = {}, projection = {}, options = {}) {
        return await TenantSchema.find(filter, projection, options);
    }

}

module.exports = TenantPortalModal;
